-- MtcInvoice Database Schema
-- Version: 1.0
-- Description: Database schema for MtcInvoice application with PDF management

-- Create database
CREATE DATABASE IF NOT EXISTS mtcinvoice_db;
USE mtcinvoice_db;

-- Users table for authentication
CREATE TABLE `users` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `email` VARCHAR(100) NOT NULL UNIQUE,
  `password` VARCHAR(255) NOT NULL,
  `role` ENUM('admin', 'staff') NOT NULL DEFAULT 'staff',
  `status` TINYINT(1) NOT NULL DEFAULT 1,
  `storage_quota` BIGINT DEFAULT 10737418240, -- 10GB default storage
  `storage_used` BIGINT DEFAULT 0,
  `last_login` DATETIME DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document categories/folders
CREATE TABLE `document_categories` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `parent_id` INT(11) DEFAULT NULL,
  `user_id` INT(11) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`parent_id`) REFERENCES `document_categories`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Documents table (for PDFs and other files)
CREATE TABLE `documents` (
  `id` CHAR(36) NOT NULL, -- UUID
  `user_id` INT(11) NOT NULL,
  `category_id` INT(11) DEFAULT NULL,
  `original_name` VARCHAR(255) NOT NULL,
  `storage_path` VARCHAR(512) NOT NULL,
  `file_name` VARCHAR(255) NOT NULL,
  `file_size` BIGINT NOT NULL, -- in bytes
  `file_type` VARCHAR(100) NOT NULL,
  `file_hash` VARCHAR(64) NOT NULL, -- SHA-256 hash for deduplication
  `is_encrypted` TINYINT(1) DEFAULT 0,
  `encryption_key` VARCHAR(255) DEFAULT NULL, -- Encrypted key if using client-side encryption
  `description` TEXT,
  `tags` JSON DEFAULT NULL,
  `is_public` TINYINT(1) DEFAULT 0,
  `download_count` INT DEFAULT 0,
  `last_downloaded_at` DATETIME DEFAULT NULL,
  `expires_at` DATETIME DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` DATETIME DEFAULT NULL, -- Soft delete
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_file_hash_user` (`file_hash`, `user_id`), -- Prevent duplicate uploads
  KEY `idx_user_category` (`user_id`, `category_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  FULLTEXT KEY `ft_search` (`original_name`, `description`, `tags`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
  FOREIGN KEY (`category_id`) REFERENCES `document_categories`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document versions
CREATE TABLE `document_versions` (
  `id` CHAR(36) NOT NULL, -- UUID
  `document_id` CHAR(36) NOT NULL,
  `version_number` INT NOT NULL,
  `file_path` VARCHAR(512) NOT NULL,
  `file_size` BIGINT NOT NULL,
  `file_hash` VARCHAR(64) NOT NULL,
  `changes` TEXT,
  `user_id` INT(11) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_doc_version` (`document_id`, `version_number`),
  FOREIGN KEY (`document_id`) REFERENCES `documents`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document access logs
CREATE TABLE `document_access_logs` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `document_id` CHAR(36) NOT NULL,
  `user_id` INT(11) NOT NULL,
  `action` ENUM('view', 'download', 'upload', 'update', 'delete', 'restore') NOT NULL,
  `ip_address` VARCHAR(45) DEFAULT NULL,
  `user_agent` TEXT,
  `metadata` JSON DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_document` (`document_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`document_id`) REFERENCES `documents`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document sharing
CREATE TABLE `document_shares` (
  `id` CHAR(36) NOT NULL, -- UUID
  `document_id` CHAR(36) NOT NULL,
  `shared_by` INT(11) NOT NULL,
  `shared_with` INT(11) NOT NULL,
  `permission` ENUM('view', 'download', 'edit') NOT NULL DEFAULT 'view',
  `token` VARCHAR(64) NOT NULL,
  `expires_at` DATETIME DEFAULT NULL,
  `download_limit` INT DEFAULT NULL,
  `is_active` TINYINT(1) DEFAULT 1,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_share_token` (`token`),
  KEY `idx_document` (`document_id`),
  KEY `idx_shared_with` (`shared_with`),
  FOREIGN KEY (`document_id`) REFERENCES `documents`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`shared_by`) REFERENCES `users`(`id`),
  FOREIGN KEY (`shared_with`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document previews (for thumbnails)
CREATE TABLE `document_previews` (
  `id` CHAR(36) NOT NULL, -- UUID
  `document_id` CHAR(36) NOT NULL,
  `page_number` INT NOT NULL,
  `image_path` VARCHAR(512) NOT NULL,
  `width` INT NOT NULL,
  `height` INT NOT NULL,
  `size` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_doc_page` (`document_id`, `page_number`),
  FOREIGN KEY (`document_id`) REFERENCES `documents`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document text content (for search)
CREATE TABLE `document_texts` (
  `document_id` CHAR(36) NOT NULL,
  `content` LONGTEXT,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`document_id`),
  FULLTEXT KEY `ft_content` (`content`),
  FOREIGN KEY (`document_id`) REFERENCES `documents`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document metadata
CREATE TABLE `document_metadata` (
  `document_id` CHAR(36) NOT NULL,
  `metadata` JSON NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`document_id`),
  FOREIGN KEY (`document_id`) REFERENCES `documents`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document comments
CREATE TABLE `document_comments` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `document_id` CHAR(36) NOT NULL,
  `user_id` INT(11) NOT NULL,
  `comment` TEXT NOT NULL,
  `page_number` INT DEFAULT NULL,
  `x_position` INT DEFAULT NULL,
  `y_position` INT DEFAULT NULL,
  `resolved` TINYINT(1) DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_document` (`document_id`),
  KEY `idx_user` (`user_id`),
  FOREIGN KEY (`document_id`) REFERENCES `documents`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document favorites
CREATE TABLE `document_favorites` (
  `user_id` INT(11) NOT NULL,
  `document_id` CHAR(36) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`, `document_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`document_id`) REFERENCES `documents`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document tags
CREATE TABLE `document_tags` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(50) NOT NULL,
  `color` VARCHAR(7) DEFAULT '#3498db',
  `user_id` INT(11) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_tag` (`user_id`, `name`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Document to tags mapping
CREATE TABLE `document_tag_mapping` (
  `document_id` CHAR(36) NOT NULL,
  `tag_id` INT(11) NOT NULL,
  PRIMARY KEY (`document_id`, `tag_id`),
  FOREIGN KEY (`document_id`) REFERENCES `documents`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`tag_id`) REFERENCES `document_tags`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Trash (for soft-deleted documents)
CREATE TABLE `trash` (
  `id` CHAR(36) NOT NULL, -- Same as document_id
  `user_id` INT(11) NOT NULL,
  `original_data` JSON NOT NULL, -- Original document data
  `deleted_by` INT(11) NOT NULL,
  `deleted_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `expires_at` TIMESTAMP NULL DEFAULT NULL, -- When to permanently delete
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_expires` (`expires_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
  FOREIGN KEY (`deleted_by`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Storage usage tracking
CREATE TABLE `storage_usage` (
  `user_id` INT(11) NOT NULL,
  `total_used` BIGINT NOT NULL DEFAULT 0,
  `document_count` INT NOT NULL DEFAULT 0,
  `last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create default admin user (password: admin123)
INSERT INTO `users` (`name`, `email`, `password`, `role`, `status`) VALUES
('Admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1);

-- Create default storage usage for admin
INSERT INTO `storage_usage` (`user_id`, `total_used`, `document_count`) VALUES
(1, 0, 0);

-- Create indexes for better performance
CREATE INDEX `idx_documents_user` ON `documents` (`user_id`);
CREATE INDEX `idx_documents_category` ON `documents` (`category_id`);
CREATE INDEX `idx_documents_created` ON `documents` (`created_at`);
CREATE INDEX `idx_documents_updated` ON `documents` (`updated_at`);
CREATE INDEX `idx_documents_deleted` ON `documents` (`deleted_at`);

-- Create a view for active documents
CREATE VIEW `v_active_documents` AS
SELECT * FROM `documents` WHERE `deleted_at` IS NULL;

-- Create a view for document statistics
CREATE VIEW `v_document_stats` AS
SELECT 
  d.user_id,
  COUNT(*) as total_documents,
  SUM(d.file_size) as total_size,
  MAX(d.created_at) as last_uploaded
FROM `documents` d
WHERE d.deleted_at IS NULL
GROUP BY d.user_id;

-- Create a view for user storage usage
CREATE VIEW `v_user_storage` AS
SELECT 
  u.id as user_id,
  u.name as user_name,
  u.email,
  u.storage_quota,
  COALESCE(su.total_used, 0) as storage_used,
  ROUND((COALESCE(su.total_used, 0) / u.storage_quota) * 100, 2) as quota_percentage_used,
  u.storage_quota - COALESCE(su.total_used, 0) as storage_remaining,
  COALESCE(su.document_count, 0) as document_count
FROM `users` u
LEFT JOIN `storage_usage` su ON u.id = su.user_id;