# MtcInvoice API

A RESTful API built with PHP 8.0+ and MySQL 8.0+ to replace Firebase functionality for the MtcInvoice Android application.

## Features

- **JWT Authentication**: Secure token-based authentication
- **Invoice Data Management**: Full CRUD operations for invoice data items
- **File Management**: Upload, download, list, and delete files (replaces Firebase Storage)
- **Statistics**: Invoice and user statistics
- **PSR Standards**: Follows PHP-FIG standards
- **Simple Architecture**: Clean, maintainable code without unnecessary complexity

## Installation

### Prerequisites

- PHP 8.0 or higher
- MySQL 8.0 or higher
- Apache/Nginx web server
- Composer (optional, for future dependencies)

### Setup

1. **Clone/Copy the API files** to your web server directory:
   ```
   /your-web-root/MtcInvoiceMasudvi/api/
   ```

2. **Configure Database** in `config/database.php`:
   ```php
   private $host = 'localhost';
   private $db_name = 'mtcinvoice_db';
   private $username = 'root';
   private $password = '';
   ```

3. **Run Database Setup**:
   Visit: `http://your-domain/MtcInvoiceMasudvi/api/setup/install.php`
   
   This will:
   - Create the database and tables
   - Set up the upload directories
   - Create a default admin user

4. **Default Admin Credentials**:
   - Email: `<EMAIL>`
   - Password: `admin123`

5. **Test the API**:
   Visit: `http://your-domain/MtcInvoiceMasudvi/api/test/test_api.php`

## API Endpoints

### Authentication

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/auth/login` | User login | No |
| POST | `/auth/register` | User registration | No |
| GET | `/auth/verify` | Token verification | No |

### Invoice Data Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/invoice` | Get all invoice items | No |
| GET | `/invoice/{id}` | Get specific invoice item | No |
| POST | `/invoice` | Create new invoice item | Yes |
| PUT | `/invoice/{id}` | Update invoice item | Yes |
| DELETE | `/invoice/{id}` | Delete invoice item | Yes |
| GET | `/invoice/stats` | Get invoice statistics | Yes |

### File Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/files/upload` | Upload file | Yes |
| GET | `/files/download?file={filename}` | Download file | No |
| GET | `/files/list` | List all files | Yes |
| DELETE | `/files/delete?file={filename}` | Delete file | Yes |

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer {your-jwt-token}
```

### Login Example

```bash
curl -X POST http://your-domain/MtcInvoiceMasudvi/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

## Data Structures

### Invoice Data Item

```json
{
  "id": "firebase-style-push-key",
  "description": "Invoice description",
  "location": "Location",
  "qr": "QR code data",
  "lpo": "Local Purchase Order",
  "inb": "Invoice number",
  "amount": "1000.00",
  "w_a": "Additional field",
  "paymentStatus": "pending|paid|overdue",
  "timestamp": 1640995200000,
  "currentDateTime": "12:00 PM, 01/01/2024"
}
```

### User

```json
{
  "id": 1,
  "name": "User Name",
  "email": "<EMAIL>",
  "role": "admin|staff",
  "status": 1,
  "last_login": "2024-01-01 12:00:00"
}
```

## Firebase Compatibility

The API maintains compatibility with the existing Firebase data structure:

- **Push Keys**: Generates Firebase-style push keys for invoice items
- **Timestamp Format**: Uses milliseconds timestamp like Firebase
- **Data Structure**: Maintains the exact same field names and types
- **Response Format**: Returns data in Firebase-compatible format

## Error Handling

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {...},
  "timestamp": "2024-01-01 12:00:00"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": 400,
    "timestamp": "2024-01-01 12:00:00"
  }
}
```

## File Upload

Files are uploaded to `/uploads/pdfs/` directory. Supported formats:
- PDF, JPG, JPEG, PNG, DOC, DOCX
- Maximum file size: 50MB

## Security Features

- JWT token authentication
- Input sanitization
- SQL injection prevention (prepared statements)
- File type validation
- Directory traversal prevention
- CORS headers
- Security headers (XSS protection, content type options)

## Configuration

Key configuration options in `config/config.php`:

```php
define('JWT_SECRET_KEY', 'your-secret-key');
define('JWT_EXPIRATION_TIME', 3600 * 24); // 24 hours
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('BASE_URL', 'http://your-domain/MtcInvoiceMasudvi/api/');
```

## Development

### Adding New Endpoints

1. Create a new PHP file in the appropriate directory
2. Include required configuration and models
3. Implement request handling logic
4. Add URL rewrite rules to `.htaccess`

### Database Migrations

To modify the database schema:
1. Update `database/schema.sql`
2. Create migration scripts if needed
3. Test with the setup script

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running

2. **File Upload Errors**
   - Check directory permissions for `/uploads/`
   - Verify PHP upload settings (`upload_max_filesize`, `post_max_size`)

3. **JWT Token Issues**
   - Ensure correct Authorization header format
   - Check token expiration time

4. **CORS Errors**
   - Verify `.htaccess` CORS headers
   - Check web server configuration

### Logs

API activities are logged when `DEBUG_MODE` is enabled. Check your PHP error logs for detailed information.

## License

This API is part of the MtcInvoice project and follows the same licensing terms.
