// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    dependencies {
        classpath 'com.android.tools.build:gradle:8.10.1'
        classpath 'com.google.gms:google-services:4.4.1'  // Google Services plugin
    }
}

plugins {
    id 'com.android.application' version '8.10.1' apply false
    id 'com.android.library' version '8.10.1' apply false
}

tasks.register('clean', Delete) {
    delete rootProject.layout.buildDirectory
}