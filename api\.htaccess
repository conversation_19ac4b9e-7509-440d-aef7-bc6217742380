RewriteEngine On

# Enable CORS
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# Handle preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# API Routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Authentication routes
RewriteRule ^auth/login/?$ auth/login.php [L,QSA]
RewriteRule ^auth/register/?$ auth/register.php [L,QSA]
RewriteRule ^auth/verify/?$ auth/verify.php [L,QSA]

# Invoice routes
RewriteRule ^invoice/?$ invoice/index.php [L,QSA]
RewriteRule ^invoice/([^/]+)/?$ invoice/index.php/$1 [L,QSA]
RewriteRule ^invoice/stats/?$ invoice/stats.php [L,QSA]

# File routes
RewriteRule ^files/upload/?$ files/upload.php [L,QSA]
RewriteRule ^files/download/?$ files/download.php [L,QSA]
RewriteRule ^files/list/?$ files/list.php [L,QSA]
RewriteRule ^files/delete/?$ files/delete.php [L,QSA]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Prevent access to sensitive files
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

<Files "config/*">
    Order deny,allow
    Deny from all
</Files>

<Files ".env">
    Order deny,allow
    Deny from all
</Files>
