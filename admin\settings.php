<?php
/**
 * Settings Page
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require login
requireLogin();

$current_page = 'settings';
$page_title = 'Settings';

include 'includes/header.php';
?>

<!-- Settings Content -->
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">Settings</h1>
        <p class="text-muted">System configuration and preferences</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- API Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>
                    API Configuration
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">API Base URL</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="<?= API_BASE_URL ?>" readonly>
                            <button class="btn btn-outline-secondary" onclick="copyToClipboard('<?= API_BASE_URL ?>')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Admin Panel URL</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="<?= ADMIN_URL ?>" readonly>
                            <button class="btn btn-outline-secondary" onclick="copyToClipboard('<?= ADMIN_URL ?>')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Database Host</label>
                        <input type="text" class="form-control" value="<?= DB_HOST ?>" readonly>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Database Name</label>
                        <input type="text" class="form-control" value="<?= DB_NAME ?>" readonly>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">PHP Version</label>
                        <input type="text" class="form-control" value="<?= PHP_VERSION ?>" readonly>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Server Software</label>
                        <input type="text" class="form-control" value="<?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?>" readonly>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Upload Max Size</label>
                        <input type="text" class="form-control" value="<?= ini_get('upload_max_filesize') ?>" readonly>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Memory Limit</label>
                        <input type="text" class="form-control" value="<?= ini_get('memory_limit') ?>" readonly>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Database Status -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-database me-2"></i>
                    Database Status
                </h5>
            </div>
            <div class="card-body">
                <?php
                try {
                    $pdo = getDbConnection();
                    
                    // Check table status
                    $tables = ['users', 'invoice_data_items', 'documents', 'document_categories'];
                    $table_status = [];
                    
                    foreach ($tables as $table) {
                        try {
                            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                            $count = $stmt->fetch()['count'];
                            $table_status[$table] = ['status' => 'OK', 'count' => $count];
                        } catch (Exception $e) {
                            $table_status[$table] = ['status' => 'ERROR', 'error' => $e->getMessage()];
                        }
                    }
                    
                    echo '<div class="alert alert-success"><i class="bi bi-check-circle me-2"></i>Database connection successful</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger"><i class="bi bi-x-circle me-2"></i>Database connection failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
                    $table_status = [];
                }
                ?>
                
                <?php if (!empty($table_status)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Table</th>
                                    <th>Status</th>
                                    <th>Records</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($table_status as $table => $status): ?>
                                    <tr>
                                        <td><code><?= $table ?></code></td>
                                        <td>
                                            <?php if ($status['status'] === 'OK'): ?>
                                                <span class="badge bg-success">OK</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">ERROR</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($status['status'] === 'OK'): ?>
                                                <?= number_format($status['count']) ?>
                                            <?php else: ?>
                                                <small class="text-danger"><?= htmlspecialchars($status['error']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= API_BASE_URL ?>" class="btn btn-outline-primary" target="_blank">
                        <i class="bi bi-box-arrow-up-right me-2"></i>
                        View API Documentation
                    </a>
                    <a href="<?= API_BASE_URL ?>test/test_api.php" class="btn btn-outline-success" target="_blank">
                        <i class="bi bi-check-circle me-2"></i>
                        Test API Endpoints
                    </a>
                    <button class="btn btn-outline-warning" onclick="clearCache()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        Clear Cache
                    </button>
                    <a href="<?= ADMIN_URL ?>logout.php" class="btn btn-outline-danger">
                        <i class="bi bi-box-arrow-right me-2"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Version Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-tag me-2"></i>
                    Version Information
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td><strong>Admin Panel:</strong></td>
                        <td><?= ADMIN_VERSION ?></td>
                    </tr>
                    <tr>
                        <td><strong>API Version:</strong></td>
                        <td>1.0.0</td>
                    </tr>
                    <tr>
                        <td><strong>Last Updated:</strong></td>
                        <td><?= date('M j, Y') ?></td>
                    </tr>
                </table>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        This admin panel provides a complete interface for managing the MtcInvoice system, 
                        replacing Firebase functionality with a custom PHP/MySQL backend.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$extra_js = "
<script>
function clearCache() {
    if (confirm('Are you sure you want to clear the cache?')) {
        showToast('Cache cleared successfully', 'success');
    }
}
</script>
";

include 'includes/footer.php';
?>
