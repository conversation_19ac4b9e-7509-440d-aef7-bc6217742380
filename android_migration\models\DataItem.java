package com.official.invoicegenarator.models;

import com.google.gson.annotations.SerializedName;

/**
 * Enhanced DataItem model with sync capabilities
 * Maintains Firebase compatibility while adding local database support
 */
public class DataItem {
    
    // Firebase-compatible fields
    @SerializedName("id")
    private String id;
    
    @SerializedName("description")
    private String description;
    
    @SerializedName("location")
    private String location;
    
    @SerializedName("qr")
    private String qr;
    
    @SerializedName("lpo")
    private String lpo;
    
    @SerializedName("inb")
    private String inb;
    
    @SerializedName("amount")
    private String amount;
    
    @SerializedName("w_a")
    private String w_a;
    
    @SerializedName("paymentStatus")
    private String paymentStatus;
    
    @SerializedName("timestamp")
    private long timestamp;
    
    @SerializedName("currentDateTime")
    private String currentDateTime;
    
    // Local database fields (not sent to API)
    private transient boolean isSynced = false;
    private transient long lastSyncTime = 0;
    private transient String syncStatus = "pending"; // "pending", "synced", "error"
    private transient String localId; // Local database ID
    private transient boolean isDeleted = false;
    private transient long createdAt;
    private transient long updatedAt;
    
    // Constructors
    public DataItem() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = this.createdAt;
    }
    
    public DataItem(String description, String location, String qr, String lpo, 
                   String inb, String amount, String w_a, String paymentStatus, long timestamp) {
        this();
        this.description = description;
        this.location = location;
        this.qr = qr;
        this.lpo = lpo;
        this.inb = inb;
        this.amount = amount;
        this.w_a = w_a;
        this.paymentStatus = paymentStatus;
        this.timestamp = timestamp;
        this.currentDateTime = formatTimestamp(timestamp);
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
        markAsUpdated();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        markAsUpdated();
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
        markAsUpdated();
    }
    
    public String getQr() {
        return qr;
    }
    
    public void setQr(String qr) {
        this.qr = qr;
        markAsUpdated();
    }
    
    public String getLpo() {
        return lpo;
    }
    
    public void setLpo(String lpo) {
        this.lpo = lpo;
        markAsUpdated();
    }
    
    public String getInb() {
        return inb;
    }
    
    public void setInb(String inb) {
        this.inb = inb;
        markAsUpdated();
    }
    
    public String getAmount() {
        return amount;
    }
    
    public void setAmount(String amount) {
        this.amount = amount;
        markAsUpdated();
    }
    
    public String getW_a() {
        return w_a;
    }
    
    public void setW_a(String w_a) {
        this.w_a = w_a;
        markAsUpdated();
    }
    
    public String getPaymentStatus() {
        return paymentStatus;
    }
    
    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
        markAsUpdated();
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
        this.currentDateTime = formatTimestamp(timestamp);
        markAsUpdated();
    }
    
    public String getCurrentDateTime() {
        return currentDateTime;
    }
    
    public void setCurrentDateTime(String currentDateTime) {
        this.currentDateTime = currentDateTime;
    }
    
    // Sync-related getters and setters
    public boolean isSynced() {
        return isSynced;
    }
    
    public void setSynced(boolean synced) {
        this.isSynced = synced;
        if (synced) {
            this.lastSyncTime = System.currentTimeMillis();
            this.syncStatus = "synced";
        }
    }
    
    public long getLastSyncTime() {
        return lastSyncTime;
    }
    
    public void setLastSyncTime(long lastSyncTime) {
        this.lastSyncTime = lastSyncTime;
    }
    
    public String getSyncStatus() {
        return syncStatus;
    }
    
    public void setSyncStatus(String syncStatus) {
        this.syncStatus = syncStatus;
    }
    
    public String getLocalId() {
        return localId;
    }
    
    public void setLocalId(String localId) {
        this.localId = localId;
    }
    
    public boolean isDeleted() {
        return isDeleted;
    }
    
    public void setDeleted(boolean deleted) {
        this.isDeleted = deleted;
        markAsUpdated();
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public long getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Helper methods
    private void markAsUpdated() {
        this.updatedAt = System.currentTimeMillis();
        if (this.isSynced) {
            this.isSynced = false;
            this.syncStatus = "pending";
        }
    }
    
    private String formatTimestamp(long timestamp) {
        // Format timestamp to match Firebase format: "h:i A, d/m/Y"
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("h:mm a, dd/MM/yyyy", java.util.Locale.getDefault());
        return sdf.format(new java.util.Date(timestamp));
    }
    
    /**
     * Check if item needs sync
     */
    public boolean needsSync() {
        return !isSynced || "pending".equals(syncStatus) || "error".equals(syncStatus);
    }
    
    /**
     * Mark as sync error
     */
    public void markSyncError() {
        this.syncStatus = "error";
        this.isSynced = false;
    }
    
    /**
     * Reset sync status for retry
     */
    public void resetSyncStatus() {
        this.syncStatus = "pending";
        this.isSynced = false;
    }
    
    /**
     * Create a copy for editing
     */
    public DataItem copy() {
        DataItem copy = new DataItem();
        copy.id = this.id;
        copy.description = this.description;
        copy.location = this.location;
        copy.qr = this.qr;
        copy.lpo = this.lpo;
        copy.inb = this.inb;
        copy.amount = this.amount;
        copy.w_a = this.w_a;
        copy.paymentStatus = this.paymentStatus;
        copy.timestamp = this.timestamp;
        copy.currentDateTime = this.currentDateTime;
        copy.isSynced = this.isSynced;
        copy.lastSyncTime = this.lastSyncTime;
        copy.syncStatus = this.syncStatus;
        copy.localId = this.localId;
        copy.isDeleted = this.isDeleted;
        copy.createdAt = this.createdAt;
        copy.updatedAt = this.updatedAt;
        return copy;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        DataItem dataItem = (DataItem) obj;
        return id != null ? id.equals(dataItem.id) : dataItem.id == null;
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
    
    @Override
    public String toString() {
        return "DataItem{" +
                "id='" + id + '\'' +
                ", description='" + description + '\'' +
                ", location='" + location + '\'' +
                ", amount='" + amount + '\'' +
                ", paymentStatus='" + paymentStatus + '\'' +
                ", syncStatus='" + syncStatus + '\'' +
                '}';
    }
}
