package com.official.invoicegenarator;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.CompoundButton;
import androidx.appcompat.app.AppCompatActivity;
import com.suke.widget.SwitchButton;

public class FingerprintSettingsActivity extends AppCompatActivity {

    private static final String PREFS_NAME = "FingerprintPrefs";
    private static final String KEY_FINGERPRINT_ENABLED = "fingerprintEnabled";
    private SwitchButton fingerprintSwitch;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_fingerprint_settings);

        // Initialize the switch
        fingerprintSwitch = findViewById(R.id.sc_fingerprint);

        // Load the saved state
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        boolean isFingerprintEnabled = prefs.getBoolean(KEY_FINGERPRINT_ENABLED, false);
        fingerprintSwitch.setChecked(isFingerprintEnabled);

        // Set a listener for the switch's state changes
        fingerprintSwitch.setOnCheckedChangeListener(new SwitchButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(SwitchButton buttonView, boolean isChecked) {
                handleFingerprintSwitch(isChecked);
            }
        });
    }

    /**
     * Handle the fingerprint switch state change
     * @param isChecked true if the switch is turned on, false otherwise
     */
    private void handleFingerprintSwitch(boolean isChecked) {
        // Save the switch state in SharedPreferences
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_FINGERPRINT_ENABLED, isChecked);
        editor.apply();

        // Optionally, you can update VarifyActivity to reflect the change
        Intent intent = new Intent(FingerprintSettingsActivity.this, VarifyActivity.class);
        intent.putExtra("FINGERPRINT_ENABLED", isChecked);
        startActivity(intent);
    }
}
