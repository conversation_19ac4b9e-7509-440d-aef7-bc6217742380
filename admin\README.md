# MtcInvoice Admin Panel

A comprehensive web-based admin panel for managing the MtcInvoice system, built with PHP, MySQL, and Bootstrap 5.

## Features

### 🎯 **Core Functionality**
- **Dashboard**: Real-time statistics and charts
- **Invoice Management**: Full CRUD operations for invoice data
- **File Management**: Upload, download, and manage PDF files
- **User Management**: Admin and staff user management
- **Settings**: System configuration and status

### 🎨 **Modern UI/UX**
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Bootstrap 5**: Modern, clean interface
- **Interactive Charts**: Chart.js integration for data visualization
- **Real-time Updates**: AJAX-powered operations
- **Toast Notifications**: User-friendly feedback system

### 🔒 **Security Features**
- **Session-based Authentication**: Secure login system
- **Role-based Access Control**: Admin and staff roles
- **Input Sanitization**: XSS protection
- **CSRF Protection**: Form security
- **Password Hashing**: Secure password storage

## Installation

### Prerequisites
- PHP 8.0 or higher
- MySQL 8.0 or higher
- Web server (Apache/Nginx)
- MtcInvoice API (must be installed first)

### Setup Steps

1. **Ensure API is Running**
   - The admin panel requires the MtcInvoice API to be installed and running
   - Visit: `http://your-domain/MtcInvoiceMasudvi/api/setup/create_tables.php`

2. **Access Admin Panel**
   - Navigate to: `http://your-domain/MtcInvoiceMasudvi/admin/login.php`

3. **Default Login Credentials**
   ```
   Email: <EMAIL>
   Password: admin123
   ```

4. **Change Default Password**
   - Login and immediately change the default password
   - Go to Settings or User Management to update credentials

## Directory Structure

```
admin/
├── config/
│   └── config.php              # Configuration settings
├── includes/
│   ├── header.php              # Common header template
│   └── footer.php              # Common footer template
├── index.php                   # Dashboard
├── login.php                   # Login page
├── logout.php                  # Logout handler
├── invoices.php                # Invoice data management
├── invoice_form.php            # Add/edit invoice form
├── files.php                   # File management
├── users.php                   # User management (admin only)
├── settings.php                # System settings
└── README.md                   # This file
```

## Page Overview

### 📊 **Dashboard** (`index.php`)
- **Statistics Cards**: Total invoices, paid invoices, total amount, active users
- **Monthly Trends Chart**: Line chart showing invoice trends over time
- **Payment Status Chart**: Pie chart showing payment status distribution
- **Recent Invoices**: Table of the 5 most recent invoices

### 📋 **Invoice Management** (`invoices.php`)
- **Search & Filter**: Search by description, location, QR, LPO, INB
- **Status Filter**: Filter by payment status (paid, pending, overdue)
- **Pagination**: Configurable page sizes (10, 20, 50, 100)
- **Export**: CSV export functionality
- **CRUD Operations**: Create, read, update, delete invoices

### 📝 **Invoice Form** (`invoice_form.php`)
- **Add New Invoice**: Create new invoice data items
- **Edit Existing**: Update invoice information
- **Validation**: Client and server-side validation
- **Firebase Compatibility**: Maintains Firebase data structure

### 📁 **File Management** (`files.php`)
- **File Upload**: Drag-and-drop file upload interface
- **File Types**: Support for PDF, JPG, PNG, DOC, DOCX
- **File Operations**: View, download, delete files
- **Storage Statistics**: File count, total size, type breakdown

### 👥 **User Management** (`users.php`) - Admin Only
- **User CRUD**: Create, read, update, delete users
- **Role Management**: Admin and staff roles
- **Status Control**: Activate/deactivate users
- **User Statistics**: Total, active, admin, staff counts

### ⚙️ **Settings** (`settings.php`)
- **API Configuration**: API URLs and settings
- **System Information**: PHP version, server info
- **Database Status**: Connection status and table information
- **Quick Actions**: API documentation, testing, cache clearing

## Configuration

### Database Settings
Edit `config/config.php` to match your database configuration:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'mtcinvoice_db');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### API Integration
The admin panel communicates with the MtcInvoice API:

```php
define('API_BASE_URL', 'http://*************/MtcInvoiceMasudvi/api/');
```

### Session Configuration
```php
define('SESSION_TIMEOUT', 3600 * 8); // 8 hours
define('SESSION_NAME', 'mtc_admin_session');
```

## Security Considerations

### Authentication
- Session-based authentication with timeout
- Password hashing using PHP's `password_hash()`
- Role-based access control

### Input Validation
- All user inputs are sanitized using `htmlspecialchars()`
- SQL injection prevention with prepared statements
- File upload validation and type checking

### CSRF Protection
- CSRF tokens for form submissions
- Token validation on sensitive operations

## API Integration

The admin panel integrates seamlessly with the MtcInvoice API:

### Authentication
- Uses the same user database as the API
- Session-based authentication for web interface
- API calls use appropriate endpoints

### Data Management
- Invoice data operations use direct database access for performance
- File operations integrate with API endpoints
- User management uses database operations

## Customization

### Styling
The admin panel uses Bootstrap 5 with custom CSS:
- Gradient backgrounds and modern design
- Responsive layout for all screen sizes
- Custom color scheme matching the brand

### Adding New Pages
1. Create new PHP file in the admin directory
2. Include the header and footer templates
3. Set `$current_page` variable for navigation highlighting
4. Follow the existing code structure and patterns

### Extending Functionality
- Add new menu items in `includes/header.php`
- Create new API endpoints if needed
- Follow the MVC pattern used throughout the application

## Troubleshooting

### Common Issues

1. **Login Failed**
   - Check database connection
   - Verify user exists and is active
   - Ensure correct credentials

2. **API Connection Issues**
   - Verify API is running and accessible
   - Check API_BASE_URL configuration
   - Test API endpoints directly

3. **File Upload Problems**
   - Check PHP upload settings (`upload_max_filesize`, `post_max_size`)
   - Verify directory permissions for `/uploads/`
   - Ensure file types are allowed

4. **Database Errors**
   - Check database connection settings
   - Verify tables exist (run setup script)
   - Check MySQL service status

### Debug Mode
Enable debug mode in `config/config.php`:
```php
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

## Browser Compatibility

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

## Performance

### Optimization Features
- Efficient database queries with proper indexing
- Pagination for large datasets
- Lazy loading for charts and images
- Minified CSS and JavaScript

### Caching
- Browser caching for static assets
- Session-based caching for user data
- Database query optimization

## License

This admin panel is part of the MtcInvoice project and follows the same licensing terms.

## Support

For support and documentation:
- Check the API documentation at `/api/`
- Review the settings page for system status
- Test API endpoints at `/api/test/test_api.php`
