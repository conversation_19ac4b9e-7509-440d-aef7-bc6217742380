package com.official.invoicegenarator.network;

import android.util.Log;

import com.google.gson.reflect.TypeToken;
import com.official.invoicegenarator.models.DataItem;
import com.official.invoicegenarator.models.FileInfo;
import com.official.invoicegenarator.models.LoginResponse;
import com.official.invoicegenarator.models.User;

import java.io.File;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API Service for MtcInvoice backend operations
 * Provides high-level methods for all API interactions
 */
public class ApiService {
    private static final String TAG = "ApiService";
    private ApiClient apiClient;
    
    public ApiService() {
        this.apiClient = ApiClient.getInstance();
    }
    
    // ==================== Authentication ====================
    
    /**
     * Login user with email and password
     */
    public void login(String email, String password, ApiCallback<LoginResponse> callback) {
        Map<String, String> loginData = new HashMap<>();
        loginData.put("email", email);
        loginData.put("password", password);
        
        apiClient.post("auth/login", loginData, new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<LoginResponse> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<LoginResponse>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    // Store auth token
                    LoginResponse loginResponse = apiResponse.getData();
                    if (loginResponse != null && loginResponse.getToken() != null) {
                        apiClient.setAuthToken(loginResponse.getToken());
                    }
                    callback.onSuccess(loginResponse);
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "Login failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Verify current authentication token
     */
    public void verifyToken(ApiCallback<User> callback) {
        apiClient.get("auth/verify", new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<User> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<User>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    callback.onSuccess(apiResponse.getData());
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "Token verification failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Logout user (clear token)
     */
    public void logout() {
        apiClient.clearAuthToken();
    }
    
    // ==================== Invoice Data Operations ====================
    
    /**
     * Create new invoice data item
     */
    public void createInvoiceItem(DataItem item, ApiCallback<DataItem> callback) {
        apiClient.post("invoice", item, new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<Map<String, Object>> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<Map<String, Object>>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    Map<String, Object> data = apiResponse.getData();
                    if (data != null && data.containsKey("item")) {
                        DataItem createdItem = apiClient.parseResponse(
                            apiClient.toJson(data.get("item")), DataItem.class);
                        callback.onSuccess(createdItem);
                    } else {
                        callback.onError("Invalid response format");
                    }
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "Create failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Get all invoice data items
     */
    public void getInvoiceItems(ApiCallback<List<DataItem>> callback) {
        getInvoiceItems(null, null, callback);
    }
    
    /**
     * Get invoice data items with pagination
     */
    public void getInvoiceItems(Integer limit, Integer offset, ApiCallback<List<DataItem>> callback) {
        String endpoint = "invoice";
        if (limit != null || offset != null) {
            endpoint += "?";
            if (limit != null) endpoint += "limit=" + limit;
            if (offset != null) {
                if (limit != null) endpoint += "&";
                endpoint += "offset=" + offset;
            }
        }
        
        apiClient.get(endpoint, new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<Map<String, Object>> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<Map<String, Object>>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    Map<String, Object> data = apiResponse.getData();
                    if (data != null && data.containsKey("items")) {
                        // Convert Firebase-style map to list
                        Map<String, Object> itemsMap = (Map<String, Object>) data.get("items");
                        List<DataItem> items = convertFirebaseMapToList(itemsMap);
                        callback.onSuccess(items);
                    } else {
                        callback.onError("Invalid response format");
                    }
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "Get items failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Get specific invoice data item by ID
     */
    public void getInvoiceItem(String id, ApiCallback<DataItem> callback) {
        apiClient.get("invoice/" + id, new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<DataItem> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<DataItem>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    callback.onSuccess(apiResponse.getData());
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "Get item failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Update invoice data item
     */
    public void updateInvoiceItem(String id, DataItem item, ApiCallback<DataItem> callback) {
        apiClient.put("invoice/" + id, item, new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<Map<String, Object>> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<Map<String, Object>>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    Map<String, Object> data = apiResponse.getData();
                    if (data != null && data.containsKey("item")) {
                        DataItem updatedItem = apiClient.parseResponse(
                            apiClient.toJson(data.get("item")), DataItem.class);
                        callback.onSuccess(updatedItem);
                    } else {
                        callback.onError("Invalid response format");
                    }
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "Update failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Delete invoice data item
     */
    public void deleteInvoiceItem(String id, ApiCallback<Void> callback) {
        apiClient.delete("invoice/" + id, new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<Void> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<Void>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    callback.onSuccess(null);
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "Delete failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Search invoice data items
     */
    public void searchInvoiceItems(String searchTerm, ApiCallback<List<DataItem>> callback) {
        apiClient.get("invoice?search=" + searchTerm, new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<Map<String, Object>> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<Map<String, Object>>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    Map<String, Object> data = apiResponse.getData();
                    if (data != null && data.containsKey("items")) {
                        Map<String, Object> itemsMap = (Map<String, Object>) data.get("items");
                        List<DataItem> items = convertFirebaseMapToList(itemsMap);
                        callback.onSuccess(items);
                    } else {
                        callback.onError("Invalid response format");
                    }
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "Search failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    // ==================== File Operations ====================
    
    /**
     * Upload file
     */
    public void uploadFile(File file, ApiCallback<FileInfo> callback) {
        apiClient.uploadFile("files/upload", file, new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<FileInfo> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<FileInfo>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    callback.onSuccess(apiResponse.getData());
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "Upload failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Download file
     */
    public void downloadFile(String filename, ApiCallback<byte[]> callback) {
        apiClient.downloadFile("files/download?file=" + filename, callback);
    }
    
    /**
     * List all files
     */
    public void listFiles(ApiCallback<List<FileInfo>> callback) {
        apiClient.get("files/list", new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<Map<String, Object>> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<Map<String, Object>>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    Map<String, Object> data = apiResponse.getData();
                    if (data != null && data.containsKey("files")) {
                        List<FileInfo> files = apiClient.parseResponse(
                            apiClient.toJson(data.get("files")), 
                            new TypeToken<List<FileInfo>>(){}.getType());
                        callback.onSuccess(files);
                    } else {
                        callback.onError("Invalid response format");
                    }
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "List files failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Delete file
     */
    public void deleteFile(String filename, ApiCallback<Void> callback) {
        apiClient.delete("files/delete?file=" + filename, new ApiCallback<String>() {
            @Override
            public void onSuccess(String response) {
                ApiResponse<Void> apiResponse = parseResponse(response, 
                    new TypeToken<ApiResponse<Void>>(){}.getType());
                
                if (apiResponse != null && apiResponse.isSuccess()) {
                    callback.onSuccess(null);
                } else {
                    callback.onError(apiResponse != null ? apiResponse.getMessage() : "Delete file failed");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    // ==================== Helper Methods ====================
    
    /**
     * Parse API response
     */
    private <T> T parseResponse(String json, Type type) {
        try {
            return apiClient.parseResponse(json, type);
        } catch (Exception e) {
            Log.e(TAG, "Failed to parse response", e);
            return null;
        }
    }
    
    /**
     * Convert Firebase-style map to list of DataItems
     */
    private List<DataItem> convertFirebaseMapToList(Map<String, Object> itemsMap) {
        // Implementation to convert Firebase-style map to list
        // This would iterate through the map and create DataItem objects
        // For now, return empty list - implement based on actual response format
        return new java.util.ArrayList<>();
    }
}
