package com.official.invoicegenarator.expense;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.official.invoicegenarator.R;

import java.util.List;

public class IncomeAdapter extends RecyclerView.Adapter<IncomeAdapter.IncomeViewHolder> {

    private List<Income> incomeList;

    public IncomeAdapter(List<Income> incomeList) {
        this.incomeList = incomeList;
    }

    @NonNull
    @Override
    public IncomeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_income, parent, false);
        return new IncomeViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull IncomeViewHolder holder, int position) {
        Income income = incomeList.get(position);
        holder.tvAmount.setText(String.valueOf(income.getAmount()));
        holder.tvCategory.setText(income.getCategory());
        holder.tvDate.setText(income.getDate());
        holder.tvNote.setText(income.getNote());
    }

    @Override
    public int getItemCount() {
        return incomeList.size();
    }

    public void updateData(List<Income> newIncomeList) {
        this.incomeList = newIncomeList;
        notifyDataSetChanged();
    }

    static class IncomeViewHolder extends RecyclerView.ViewHolder {

        TextView tvAmount, tvCategory, tvDate, tvNote;

        IncomeViewHolder(View itemView) {
            super(itemView);
            tvAmount = itemView.findViewById(R.id.tvAmount);
            tvCategory = itemView.findViewById(R.id.tvCategory);
            tvDate = itemView.findViewById(R.id.tvDate);
            tvNote = itemView.findViewById(R.id.tvNote);
        }
    }
}
