package com.official.invoicegenarator;

import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.google.android.material.tabs.TabLayout;
import com.official.invoicegenarator.expense.ExpenseFragment;
import com.official.invoicegenarator.expense.IncomeFragment;

public class MoneyBagActivity extends AppCompatActivity {

    private TabLayout tabLayout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_money_bag);

       /* tabLayout = findViewById(R.id.tabLayout);

        // Set up the initial fragment
        if (savedInstanceState == null) {
            loadFragment(new ExpenseFragment()); // Default fragment
        }

        // Set up tab selection listener
        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                switch (tab.getPosition()) {
                    case 0:
                        loadFragment(new ExpenseFragment());
                        break;
                    case 1:
                        loadFragment(new IncomeFragment());
                        break;
                    case 2:
                       // loadFragment(new SummaryFragment());
                        break;
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                // Handle tab unselected
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
                // Handle tab reselected
            }
        });
    }

    private void loadFragment(Fragment fragment) {
        FragmentManager fragmentManager = getSupportFragmentManager();
        FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();
        fragmentTransaction.replace(R.id.fragment_container, fragment); // Use a FrameLayout or FragmentContainerView
        fragmentTransaction.commit();
    }*/
    }
}
