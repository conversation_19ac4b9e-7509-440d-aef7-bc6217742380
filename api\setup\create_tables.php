<?php
/**
 * Direct Table Creation Script
 * Creates essential tables for MtcInvoice API
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';

// Only allow GET requests for safety
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendErrorResponse('Method not allowed', 405);
}

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE " . DB_NAME);
    
    $tables_created = [];
    $errors = [];
    
    // Create users table
    try {
        $users_sql = "
        CREATE TABLE IF NOT EXISTS `users` (
          `id` INT(11) NOT NULL AUTO_INCREMENT,
          `name` VARCHAR(100) NOT NULL,
          `email` VARCHAR(100) NOT NULL UNIQUE,
          `password` VARCHAR(255) NOT NULL,
          `role` ENUM('admin', 'staff') NOT NULL DEFAULT 'staff',
          `status` TINYINT(1) NOT NULL DEFAULT 1,
          `storage_quota` BIGINT DEFAULT 10737418240,
          `storage_used` BIGINT DEFAULT 0,
          `last_login` DATETIME DEFAULT NULL,
          `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($users_sql);
        $tables_created[] = 'users';
    } catch (PDOException $e) {
        $errors[] = "Users table: " . $e->getMessage();
    }
    
    // Create invoice_data_items table
    try {
        $invoice_sql = "
        CREATE TABLE IF NOT EXISTS `invoice_data_items` (
          `id` VARCHAR(50) NOT NULL,
          `description` TEXT NOT NULL,
          `location` VARCHAR(255) NOT NULL,
          `qr` VARCHAR(255) NOT NULL,
          `lpo` VARCHAR(255) NOT NULL,
          `inb` VARCHAR(255) NOT NULL,
          `amount` VARCHAR(50) NOT NULL,
          `w_a` VARCHAR(255) NOT NULL,
          `payment_status` VARCHAR(100) NOT NULL,
          `timestamp` BIGINT NOT NULL,
          `current_date_time` VARCHAR(50) DEFAULT NULL,
          `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_timestamp` (`timestamp`),
          KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($invoice_sql);
        $tables_created[] = 'invoice_data_items';
    } catch (PDOException $e) {
        $errors[] = "Invoice data items table: " . $e->getMessage();
    }
    
    // Create document_categories table
    try {
        $categories_sql = "
        CREATE TABLE IF NOT EXISTS `document_categories` (
          `id` INT(11) NOT NULL AUTO_INCREMENT,
          `name` VARCHAR(100) NOT NULL,
          `parent_id` INT(11) DEFAULT NULL,
          `user_id` INT(11) NOT NULL,
          `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          FOREIGN KEY (`parent_id`) REFERENCES `document_categories`(`id`) ON DELETE CASCADE,
          FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($categories_sql);
        $tables_created[] = 'document_categories';
    } catch (PDOException $e) {
        $errors[] = "Document categories table: " . $e->getMessage();
    }
    
    // Create documents table
    try {
        $documents_sql = "
        CREATE TABLE IF NOT EXISTS `documents` (
          `id` CHAR(36) NOT NULL,
          `user_id` INT(11) NOT NULL,
          `category_id` INT(11) DEFAULT NULL,
          `original_name` VARCHAR(255) NOT NULL,
          `storage_path` VARCHAR(512) NOT NULL,
          `file_name` VARCHAR(255) NOT NULL,
          `file_size` BIGINT NOT NULL,
          `file_type` VARCHAR(100) NOT NULL,
          `file_hash` VARCHAR(64) NOT NULL,
          `is_encrypted` TINYINT(1) DEFAULT 0,
          `encryption_key` VARCHAR(255) DEFAULT NULL,
          `description` TEXT,
          `tags` JSON DEFAULT NULL,
          `is_public` TINYINT(1) DEFAULT 0,
          `download_count` INT DEFAULT 0,
          `last_downloaded_at` DATETIME DEFAULT NULL,
          `expires_at` DATETIME DEFAULT NULL,
          `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          `deleted_at` DATETIME DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `idx_file_hash_user` (`file_hash`, `user_id`),
          KEY `idx_user_category` (`user_id`, `category_id`),
          KEY `idx_created_at` (`created_at`),
          KEY `idx_deleted_at` (`deleted_at`),
          FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
          FOREIGN KEY (`category_id`) REFERENCES `document_categories`(`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($documents_sql);
        $tables_created[] = 'documents';
    } catch (PDOException $e) {
        $errors[] = "Documents table: " . $e->getMessage();
    }
    
    // Create storage_usage table
    try {
        $storage_sql = "
        CREATE TABLE IF NOT EXISTS `storage_usage` (
          `user_id` INT(11) NOT NULL,
          `total_used` BIGINT NOT NULL DEFAULT 0,
          `document_count` INT NOT NULL DEFAULT 0,
          `last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`user_id`),
          FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($storage_sql);
        $tables_created[] = 'storage_usage';
    } catch (PDOException $e) {
        $errors[] = "Storage usage table: " . $e->getMessage();
    }
    
    // Create uploads directory
    $upload_dir = '../uploads/pdfs/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Create default admin user if it doesn't exist
    $admin_created = false;
    try {
        $check_admin = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $admin_count = $check_admin->fetch()['count'];
        
        if ($admin_count == 0) {
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $pdo->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, ?, ?)")
                ->execute(['Admin', '<EMAIL>', $admin_password, 'admin', 1]);
            
            // Create storage usage entry for admin
            $admin_id = $pdo->lastInsertId();
            $pdo->prepare("INSERT INTO storage_usage (user_id, total_used, document_count) VALUES (?, ?, ?)")
                ->execute([$admin_id, 0, 0]);
            
            $admin_created = true;
        }
    } catch (PDOException $e) {
        $errors[] = "Failed to create admin user: " . $e->getMessage();
    }
    
    $response = [
        'database_created' => true,
        'tables_created' => $tables_created,
        'total_tables' => count($tables_created),
        'upload_directory_created' => is_dir($upload_dir),
        'admin_user_created' => $admin_created,
        'default_credentials' => [
            'email' => '<EMAIL>',
            'password' => 'admin123'
        ]
    ];
    
    if (!empty($errors)) {
        $response['errors'] = $errors;
    }
    
    sendSuccessResponse($response, 'Database tables created successfully');
    
} catch (PDOException $e) {
    sendErrorResponse('Database setup failed: ' . $e->getMessage(), 500);
} catch (Exception $e) {
    sendErrorResponse('Setup failed: ' . $e->getMessage(), 500);
}
