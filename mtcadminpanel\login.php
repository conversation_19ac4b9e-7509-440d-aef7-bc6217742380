<?php
$pageTitle = 'Login - MTC Invoice Management System';

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

include 'includes/header.php';
?>

<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left side - Login Form -->
        <div class="col-md-6 d-flex align-items-center justify-content-center">
            <div class="card shadow-lg border-0" style="width: 100%; max-width: 400px;">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-receipt display-4 text-primary"></i>
                        <h3 class="mt-3 mb-1">MTC Invoice</h3>
                        <p class="text-muted">Management System</p>
                    </div>
                    
                    <?php if (isset($_GET['expired'])): ?>
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle"></i> Your session has expired. Please login again.
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_GET['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-circle"></i> 
                            <?php 
                            switch($_GET['error']) {
                                case 'insufficient_permissions':
                                    echo 'You do not have permission to access that page.';
                                    break;
                                case 'csrf_invalid':
                                    echo 'Security token invalid. Please try again.';
                                    break;
                                default:
                                    echo 'An error occurred. Please try again.';
                            }
                            ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username or Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-person"></i></span>
                                <input type="text" class="form-control" id="username" name="username" required 
                                       placeholder="Enter your username or email">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                <input type="password" class="form-control" id="password" name="password" required 
                                       placeholder="Enter your password">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="bi bi-eye" id="passwordToggleIcon"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me">
                            <label class="form-check-label" for="rememberMe">
                                Remember me
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                                <span class="spinner-border spinner-border-sm d-none" id="loginSpinner"></span>
                                <i class="bi bi-box-arrow-in-right"></i> Login
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            Default credentials: admin / admin123
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right side - Background Image/Info -->
        <div class="col-md-6 d-none d-md-flex align-items-center justify-content-center" 
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="text-center text-white p-5">
                <i class="bi bi-graph-up display-1 mb-4"></i>
                <h2 class="mb-4">Welcome to MTC Invoice Management</h2>
                <p class="lead mb-4">
                    Streamline your invoice management process with our comprehensive system. 
                    Track invoices, manage files, and generate reports with ease.
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <i class="bi bi-receipt-cutoff display-6"></i>
                        <p class="mt-2">Invoice Tracking</p>
                    </div>
                    <div class="col-4">
                        <i class="bi bi-file-earmark-pdf display-6"></i>
                        <p class="mt-2">File Management</p>
                    </div>
                    <div class="col-4">
                        <i class="bi bi-graph-up-arrow display-6"></i>
                        <p class="mt-2">Analytics</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const loginBtn = document.getElementById('loginBtn');
    const loginSpinner = document.getElementById('loginSpinner');
    
    // Show loading state
    loginBtn.disabled = true;
    loginSpinner.classList.remove('d-none');
    
    // Prepare data
    const data = {
        username: formData.get('username'),
        password: formData.get('password'),
        remember_me: formData.get('remember_me') ? 1 : 0,
        csrf_token: CSRF_TOKEN
    };
    
    fetch('api/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': CSRF_TOKEN
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Login successful! Redirecting...', 'success');
            setTimeout(() => {
                window.location.href = data.data.redirect || 'dashboard.php';
            }, 1000);
        } else {
            showToast(data.message || 'Login failed', 'danger');
        }
    })
    .catch(error => {
        console.error('Login error:', error);
        showToast('Login failed. Please try again.', 'danger');
    })
    .finally(() => {
        // Hide loading state
        loginBtn.disabled = false;
        loginSpinner.classList.add('d-none');
    });
});

function togglePassword() {
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('bi-eye');
        toggleIcon.classList.add('bi-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('bi-eye-slash');
        toggleIcon.classList.add('bi-eye');
    }
}

// Auto-focus username field
document.getElementById('username').focus();

// Handle Enter key in form fields
document.querySelectorAll('#loginForm input').forEach(input => {
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('loginForm').dispatchEvent(new Event('submit'));
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
