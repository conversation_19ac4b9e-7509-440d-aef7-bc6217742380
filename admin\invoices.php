<?php
/**
 * Invoice Data Management
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require login
requireLogin();

$current_page = 'invoices';
$page_title = 'Invoice Data Management';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'delete' && isset($_POST['id'])) {
        try {
            $pdo = getDbConnection();
            $stmt = $pdo->prepare("DELETE FROM invoice_data_items WHERE id = ?");
            $stmt->execute([$_POST['id']]);
            showAlert('Invoice deleted successfully', 'success');
        } catch (Exception $e) {
            showAlert('Failed to delete invoice', 'danger');
            error_log("Delete error: " . $e->getMessage());
        }
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit();
    }
}

// Pagination and search
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = min(MAX_PAGE_SIZE, max(1, (int)($_GET['per_page'] ?? DEFAULT_PAGE_SIZE)));
$search = trim($_GET['search'] ?? '');
$status_filter = $_GET['status'] ?? '';

try {
    $pdo = getDbConnection();
    
    // Build query
    $where_conditions = [];
    $params = [];
    
    if ($search) {
        $where_conditions[] = "(description LIKE ? OR location LIKE ? OR qr LIKE ? OR lpo LIKE ? OR inb LIKE ?)";
        $search_term = "%$search%";
        $params = array_fill(0, 5, $search_term);
    }
    
    if ($status_filter) {
        $where_conditions[] = "payment_status = ?";
        $params[] = $status_filter;
    }
    
    $where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM invoice_data_items $where_clause";
    $stmt = $pdo->prepare($count_query);
    $stmt->execute($params);
    $total_records = $stmt->fetch()['total'];
    
    // Get paginated data
    $offset = ($page - 1) * $per_page;
    $data_query = "SELECT * FROM invoice_data_items $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
    $stmt = $pdo->prepare($data_query);
    $stmt->execute($params);
    $invoices = $stmt->fetchAll();
    
    $pagination = getPaginationInfo($total_records, $page, $per_page);
    
} catch (Exception $e) {
    error_log("Invoice list error: " . $e->getMessage());
    $invoices = [];
    $total_records = 0;
    $pagination = getPaginationInfo(0, 1, $per_page);
}

include 'includes/header.php';
?>

<!-- Invoice Management Content -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Invoice Data Management</h1>
        <p class="text-muted">Manage all invoice data items</p>
    </div>
    <div class="col-md-6 text-end">
        <a href="<?= ADMIN_URL ?>invoice_form.php" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            Add New Invoice
        </a>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= htmlspecialchars($search) ?>" 
                       placeholder="Search invoices...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Payment Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="paid" <?= $status_filter === 'paid' ? 'selected' : '' ?>>Paid</option>
                    <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>Pending</option>
                    <option value="overdue" <?= $status_filter === 'overdue' ? 'selected' : '' ?>>Overdue</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="per_page" class="form-label">Per Page</label>
                <select class="form-select" id="per_page" name="per_page">
                    <option value="10" <?= $per_page === 10 ? 'selected' : '' ?>>10</option>
                    <option value="20" <?= $per_page === 20 ? 'selected' : '' ?>>20</option>
                    <option value="50" <?= $per_page === 50 ? 'selected' : '' ?>>50</option>
                    <option value="100" <?= $per_page === 100 ? 'selected' : '' ?>>100</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search"></i> Search
                </button>
                <a href="<?= ADMIN_URL ?>invoices.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i> Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Invoice Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="bi bi-table me-2"></i>
            Invoice Data (<?= number_format($total_records) ?> total)
        </h5>
        <div>
            <button class="btn btn-outline-primary btn-sm" onclick="exportTableToCSV('invoiceTable', 'invoices.csv')">
                <i class="bi bi-download"></i> Export CSV
            </button>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($invoices)): ?>
            <div class="text-center py-5">
                <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">No invoices found</h4>
                <p class="text-muted">Try adjusting your search criteria or add a new invoice.</p>
                <a href="<?= ADMIN_URL ?>invoice_form.php" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    Add First Invoice
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="invoiceTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Description</th>
                            <th>Location</th>
                            <th>QR</th>
                            <th>LPO</th>
                            <th>INB</th>
                            <th>Amount</th>
                            <th>W/A</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoices as $invoice): ?>
                            <tr>
                                <td>
                                    <code title="<?= htmlspecialchars($invoice['id']) ?>">
                                        <?= htmlspecialchars(substr($invoice['id'], 0, 8)) ?>...
                                    </code>
                                </td>
                                <td>
                                    <div style="max-width: 150px; overflow: hidden; text-overflow: ellipsis;">
                                        <?= htmlspecialchars($invoice['description']) ?>
                                    </div>
                                </td>
                                <td><?= htmlspecialchars($invoice['location']) ?></td>
                                <td><?= htmlspecialchars($invoice['qr']) ?></td>
                                <td><?= htmlspecialchars($invoice['lpo']) ?></td>
                                <td><?= htmlspecialchars($invoice['inb']) ?></td>
                                <td>$<?= number_format($invoice['amount'], 2) ?></td>
                                <td><?= htmlspecialchars($invoice['w_a']) ?></td>
                                <td>
                                    <?php
                                    $status_class = match($invoice['payment_status']) {
                                        'paid' => 'success',
                                        'pending' => 'warning',
                                        'overdue' => 'danger',
                                        default => 'secondary'
                                    };
                                    ?>
                                    <span class="badge bg-<?= $status_class ?>">
                                        <?= ucfirst($invoice['payment_status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <small><?= formatDate($invoice['created_at'], 'M j, Y g:i A') ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= ADMIN_URL ?>invoice_form.php?id=<?= urlencode($invoice['id']) ?>" 
                                           class="btn btn-outline-primary" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteInvoice('<?= htmlspecialchars($invoice['id']) ?>')" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($pagination['total_pages'] > 1): ?>
                <nav aria-label="Invoice pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?= !$pagination['has_prev'] ? 'disabled' : '' ?>">
                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                        
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($pagination['total_pages'], $page + 2);
                        
                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                            <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <li class="page-item <?= !$pagination['has_next'] ? 'disabled' : '' ?>">
                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
                
                <div class="text-center text-muted">
                    Showing <?= $pagination['start'] ?> to <?= $pagination['end'] ?> of <?= number_format($pagination['total']) ?> entries
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Form (hidden) -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="id" id="deleteId">
</form>

<?php
$extra_js = "
<script>
function deleteInvoice(id) {
    if (confirmDelete('Are you sure you want to delete this invoice? This action cannot be undone.')) {
        document.getElementById('deleteId').value = id;
        document.getElementById('deleteForm').submit();
    }
}
</script>
";

include 'includes/footer.php';
?>
