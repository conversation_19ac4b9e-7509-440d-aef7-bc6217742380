<?php
/**
 * File Download API
 * Replaces Firebase Storage download functionality
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';
require_once '../utils/jwt.php';

try {
    // Get filename from query parameter
    $filename = $_GET['file'] ?? null;
    
    if (!$filename) {
        sendErrorResponse('Filename is required', 400);
    }
    
    // Sanitize filename to prevent directory traversal
    $filename = basename($filename);
    
    // Construct file path
    $file_path = UPLOAD_DIR . 'pdfs/' . $filename;
    
    // Check if file exists
    if (!file_exists($file_path)) {
        sendErrorResponse('File not found', 404);
    }
    
    // Get file info
    $file_size = filesize($file_path);
    $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    // Set appropriate content type
    $content_types = [
        'pdf' => 'application/pdf',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    $content_type = $content_types[$file_extension] ?? 'application/octet-stream';
    
    // Set headers for file download
    header('Content-Type: ' . $content_type);
    header('Content-Length: ' . $file_size);
    header('Content-Disposition: inline; filename="' . $filename . '"');
    header('Cache-Control: public, max-age=3600');
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
    
    // Handle range requests for large files
    $range = $_SERVER['HTTP_RANGE'] ?? null;
    
    if ($range) {
        // Parse range header
        if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
            $start = intval($matches[1]);
            $end = $matches[2] ? intval($matches[2]) : $file_size - 1;
            
            if ($start < $file_size && $end < $file_size && $start <= $end) {
                $length = $end - $start + 1;
                
                header('HTTP/1.1 206 Partial Content');
                header("Content-Range: bytes $start-$end/$file_size");
                header("Content-Length: $length");
                
                $file = fopen($file_path, 'rb');
                fseek($file, $start);
                echo fread($file, $length);
                fclose($file);
                exit;
            }
        }
    }
    
    // Log download activity
    logActivity('file_downloaded', [
        'filename' => $filename,
        'file_size' => $file_size,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);
    
    // Output file
    readfile($file_path);
    
} catch (Exception $e) {
    error_log("File download error: " . $e->getMessage());
    sendErrorResponse('File download failed', 500);
}
