<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Enter file name (without .pdf)"
        app:boxStrokeColor="@color/gray"
        app:hintAnimationEnabled="true"
        app:hintEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/fileNameInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:padding="10dp"
            android:paddingLeft="16dp"
            android:textColor="@color/black"
            android:textColorHint="@color/gray" />
    </com.google.android.material.textfield.TextInputLayout>
</LinearLayout>
