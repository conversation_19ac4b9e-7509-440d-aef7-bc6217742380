<?php
/**
 * Authentication Controller
 * MTC Invoice Management System
 */

class AuthController extends BaseController {
    private $userModel;
    
    public function __construct() {
        parent::__construct();
        $this->userModel = new User();
    }
    
    /**
     * User login
     */
    public function login() {
        try {
            $this->requireCSRF();
            $this->validateRequired(['username', 'password']);
            
            $username = $this->request['data']['username'];
            $password = $this->request['data']['password'];
            
            $user = $this->userModel->authenticate($username, $password);
            
            if ($user) {
                // Set session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['login_time'] = time();
                
                // Log activity
                $this->logActivity('LOGIN', 'users', $user['id']);
                
                $this->sendSuccess('Login successful', [
                    'user' => $user,
                    'redirect' => 'dashboard.php'
                ]);
            } else {
                $this->sendError('Invalid username or password', 401);
            }
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $this->sendError('Login failed');
        }
    }
    
    /**
     * User logout
     */
    public function logout() {
        try {
            if ($this->isAuthenticated()) {
                $this->logActivity('LOGOUT', 'users', $this->user['id']);
            }
            
            // Destroy session
            session_destroy();
            
            $this->sendSuccess('Logout successful', [
                'redirect' => 'login.php'
            ]);
            
        } catch (Exception $e) {
            error_log("Logout error: " . $e->getMessage());
            $this->sendError('Logout failed');
        }
    }
    
    /**
     * Get current user info
     */
    public function me() {
        try {
            $this->requireAuth();
            
            $this->sendSuccess('User info retrieved', $this->user);
            
        } catch (Exception $e) {
            error_log("User info error: " . $e->getMessage());
            $this->sendError('Failed to get user info');
        }
    }
    
    /**
     * Change password
     */
    public function changePassword() {
        try {
            $this->requireAuth();
            $this->requireCSRF();
            $this->validateRequired(['current_password', 'new_password', 'confirm_password']);
            
            $currentPassword = $this->request['data']['current_password'];
            $newPassword = $this->request['data']['new_password'];
            $confirmPassword = $this->request['data']['confirm_password'];
            
            // Verify current password
            $user = $this->userModel->authenticate($this->user['username'], $currentPassword);
            if (!$user) {
                $this->sendError('Current password is incorrect', 400);
            }
            
            // Validate new password
            if (strlen($newPassword) < 6) {
                $this->sendError('New password must be at least 6 characters', 400);
            }
            
            if ($newPassword !== $confirmPassword) {
                $this->sendError('New password and confirmation do not match', 400);
            }
            
            // Update password
            $success = $this->userModel->updatePassword($this->user['id'], $newPassword);
            
            if ($success) {
                $this->logActivity('CHANGE_PASSWORD', 'users', $this->user['id']);
                $this->sendSuccess('Password changed successfully');
            } else {
                $this->sendError('Failed to change password');
            }
            
        } catch (Exception $e) {
            error_log("Change password error: " . $e->getMessage());
            $this->sendError('Failed to change password');
        }
    }
    
    /**
     * Update user profile
     */
    public function updateProfile() {
        try {
            $this->requireAuth();
            $this->requireCSRF();
            
            $data = $this->request['data'];
            unset($data['csrf_token']);
            
            // Get old data for logging
            $oldData = $this->userModel->find($this->user['id']);
            
            $success = $this->userModel->updateProfile($this->user['id'], $data);
            
            if ($success) {
                // Update session data
                if (isset($data['username'])) {
                    $_SESSION['username'] = $data['username'];
                }
                
                $this->logActivity('UPDATE_PROFILE', 'users', $this->user['id'], $oldData, $data);
                $this->sendSuccess('Profile updated successfully');
            } else {
                $this->sendError('Failed to update profile');
            }
            
        } catch (InvalidArgumentException $e) {
            $this->sendError($e->getMessage(), 400);
        } catch (Exception $e) {
            error_log("Update profile error: " . $e->getMessage());
            $this->sendError('Failed to update profile');
        }
    }
    
    /**
     * Check session validity
     */
    public function checkSession() {
        try {
            if (!$this->isAuthenticated()) {
                $this->sendError('Session expired', 401);
            }
            
            // Check session timeout
            $loginTime = $_SESSION['login_time'] ?? 0;
            if (time() - $loginTime > SESSION_TIMEOUT) {
                session_destroy();
                $this->sendError('Session expired', 401);
            }
            
            $this->sendSuccess('Session valid', [
                'user' => $this->user,
                'remaining_time' => SESSION_TIMEOUT - (time() - $loginTime)
            ]);
            
        } catch (Exception $e) {
            error_log("Check session error: " . $e->getMessage());
            $this->sendError('Session check failed');
        }
    }
}
