<?php
/**
 * MtcInvoice API - Main Entry Point
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once 'config/config.php';

// API Information
$api_info = [
    'name' => APP_NAME,
    'version' => APP_VERSION,
    'description' => 'RESTful API for MtcInvoice application - Firebase replacement',
    'endpoints' => [
        'authentication' => [
            'POST /auth/login' => 'User login',
            'POST /auth/register' => 'User registration',
            'GET /auth/verify' => 'Token verification'
        ],
        'invoice_data' => [
            'GET /invoice' => 'Get all invoice items',
            'GET /invoice/{id}' => 'Get specific invoice item',
            'POST /invoice' => 'Create new invoice item',
            'PUT /invoice/{id}' => 'Update invoice item',
            'DELETE /invoice/{id}' => 'Delete invoice item',
            'GET /invoice/stats' => 'Get invoice statistics'
        ],
        'file_management' => [
            'POST /files/upload' => 'Upload file',
            'GET /files/download' => 'Download file',
            'GET /files/list' => 'List files',
            'DELETE /files/delete' => 'Delete file'
        ]
    ],
    'authentication' => [
        'type' => 'JWT Bearer Token',
        'header' => 'Authorization: Bearer {token}',
        'expiration' => JWT_EXPIRATION_TIME . ' seconds'
    ],
    'base_url' => BASE_URL,
    'timestamp' => date('Y-m-d H:i:s')
];

sendSuccessResponse($api_info, 'MtcInvoice API is running');
