package com.official.invoicegenarator.expense;

import android.content.DialogInterface;
import android.database.Cursor;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.official.invoicegenarator.R;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ExpenseFragment extends Fragment {

    private RecyclerView recyclerView;
    private ExpenseAdapter expenseAdapter;
    private DatabaseHelper databaseHelper;
    private List<Expense> expenseList;
    private PieChart pieChart;
    private Button btnAddExpense;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_expense, container, false);

        recyclerView = view.findViewById(R.id.recyclerViewExpenses);
        pieChart = view.findViewById(R.id.pieChartExpenses);
        btnAddExpense = view.findViewById(R.id.btnAddExpense);

        databaseHelper = new DatabaseHelper(getContext());

        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        expenseList = new ArrayList<>();
        expenseAdapter = new ExpenseAdapter(expenseList);
        recyclerView.setAdapter(expenseAdapter);

        loadExpenses();

        btnAddExpense.setOnClickListener(v -> showAddExpenseDialog());

        return view;
    }

    private void loadExpenses() {
        expenseList.clear();
        Cursor cursor = databaseHelper.getTransactionsByType("expense");
        if (cursor.moveToFirst()) {
            do {
                int id = cursor.getInt(cursor.getColumnIndex("id")); // Get id from the cursor
                String category = cursor.getString(cursor.getColumnIndex("category"));
                int amount = cursor.getInt(cursor.getColumnIndex("amount"));
                String date = ""; // Set to default or extract from cursor if needed
                String note = ""; // Set to default or extract from cursor if needed
                String type = cursor.getString(cursor.getColumnIndex("type")); // Retrieve the type from the cursor

                // Now create the Expense object with all required parameters
                expenseList.add(new Expense(id, amount, category, date, note, type));
            } while (cursor.moveToNext());
        }
        cursor.close();
        expenseAdapter.notifyDataSetChanged();
        updatePieChart();
    }


    private void updatePieChart() {
        List<PieEntry> entries = new ArrayList<>();
        Cursor cursor = databaseHelper.getTransactionsByType("expense");
        if (cursor.moveToFirst()) {
            do {
                String category = cursor.getString(cursor.getColumnIndex("category"));
                int amount = cursor.getInt(cursor.getColumnIndex("amount"));
                entries.add(new PieEntry(amount, category));
            } while (cursor.moveToNext());
        }
        cursor.close();

        PieDataSet dataSet = new PieDataSet(entries, "Expenses");
        PieData pieData = new PieData(dataSet);
        pieChart.setData(pieData);
        pieChart.invalidate(); // Refresh the chart
    }

    private void showAddExpenseDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("Add Expense");

        View dialogView = getLayoutInflater().inflate(R.layout.dialog_add_expense, null);
        builder.setView(dialogView);

        EditText etAmount = dialogView.findViewById(R.id.etAmount);
        Spinner spCategory = dialogView.findViewById(R.id.spCategory);
        EditText etNote = dialogView.findViewById(R.id.etNote);

        builder.setPositiveButton("Add", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                try {
                    int amount = Integer.parseInt(etAmount.getText().toString());
                    String category = spCategory.getSelectedItem().toString();
                    String note = etNote.getText().toString();
                    String date = getCurrentDate();

                    databaseHelper.addTransaction(amount, category, date, note, "expense");
                    loadExpenses();
                    Toast.makeText(getContext(), "Expense added!", Toast.LENGTH_SHORT).show();
                } catch (NumberFormatException e) {
                    Toast.makeText(getContext(), "Invalid amount", Toast.LENGTH_SHORT).show();
                }
            }
        });

        builder.setNegativeButton("Cancel", null);
        builder.show();
    }

    private String getCurrentDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        return sdf.format(new Date());
    }
}
