<?php
/**
 * API Test Script
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';

// Test configuration
$base_url = BASE_URL;
$test_results = [];

/**
 * Make HTTP request
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => array_merge([
            'Content-Type: application/json',
        ], $headers),
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_TIMEOUT => 30
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $http_code,
        'error' => $error
    ];
}

/**
 * Run test
 */
function runTest($name, $url, $method = 'GET', $data = null, $headers = [], $expected_code = 200) {
    global $test_results;
    
    $result = makeRequest($url, $method, $data, $headers);
    
    $success = $result['http_code'] == $expected_code && empty($result['error']);
    
    $test_results[] = [
        'name' => $name,
        'url' => $url,
        'method' => $method,
        'expected_code' => $expected_code,
        'actual_code' => $result['http_code'],
        'success' => $success,
        'response' => $result['response'],
        'error' => $result['error']
    ];
    
    return $result;
}

// Test 1: API Info
runTest('API Info', $base_url);

// Test 2: User Registration
$register_data = [
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'password' => 'test123',
    'role' => 'staff'
];
$register_result = runTest('User Registration', $base_url . 'auth/register', 'POST', $register_data, [], 200);

// Test 3: User Login
$login_data = [
    'email' => '<EMAIL>',
    'password' => 'admin123'
];
$login_result = runTest('User Login', $base_url . 'auth/login', 'POST', $login_data, [], 200);

// Extract token for authenticated requests
$token = null;
if ($login_result['response']) {
    $login_response = json_decode($login_result['response'], true);
    if (isset($login_response['data']['token'])) {
        $token = $login_response['data']['token'];
    }
}

$auth_headers = $token ? ['Authorization: Bearer ' . $token] : [];

// Test 4: Token Verification
if ($token) {
    runTest('Token Verification', $base_url . 'auth/verify', 'GET', null, $auth_headers, 200);
}

// Test 5: Create Invoice Item
$invoice_data = [
    'description' => 'Test Invoice Item',
    'location' => 'Test Location',
    'qr' => 'TEST-QR-001',
    'lpo' => 'LPO-001',
    'inb' => 'INB-001',
    'amount' => '1000.00',
    'w_a' => 'Test W/A',
    'payment_status' => 'pending'
];
$create_result = runTest('Create Invoice Item', $base_url . 'invoice', 'POST', $invoice_data, $auth_headers, 200);

// Extract created item ID
$item_id = null;
if ($create_result['response']) {
    $create_response = json_decode($create_result['response'], true);
    if (isset($create_response['data']['id'])) {
        $item_id = $create_response['data']['id'];
    }
}

// Test 6: Get All Invoice Items
runTest('Get All Invoice Items', $base_url . 'invoice', 'GET', null, [], 200);

// Test 7: Get Specific Invoice Item
if ($item_id) {
    runTest('Get Specific Invoice Item', $base_url . 'invoice/' . $item_id, 'GET', null, [], 200);
}

// Test 8: Update Invoice Item
if ($item_id) {
    $update_data = [
        'description' => 'Updated Test Invoice Item',
        'payment_status' => 'paid'
    ];
    runTest('Update Invoice Item', $base_url . 'invoice/' . $item_id, 'PUT', $update_data, $auth_headers, 200);
}

// Test 9: Get Invoice Statistics
runTest('Get Invoice Statistics', $base_url . 'invoice/stats', 'GET', null, $auth_headers, 200);

// Test 10: Get File List
runTest('Get File List', $base_url . 'files/list', 'GET', null, $auth_headers, 200);

// Generate HTML report
?>
<!DOCTYPE html>
<html>
<head>
    <title>MtcInvoice API Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .failure { background-color: #f8d7da; border-color: #f5c6cb; }
        .method { font-weight: bold; color: #007bff; }
        .url { font-family: monospace; color: #6c757d; }
        .response { background-color: #f8f9fa; padding: 10px; margin-top: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
        .summary { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>MtcInvoice API Test Results</h1>
    
    <?php
    $total_tests = count($test_results);
    $passed_tests = array_filter($test_results, function($test) { return $test['success']; });
    $passed_count = count($passed_tests);
    ?>
    
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Tests:</strong> <?= $total_tests ?></p>
        <p><strong>Passed:</strong> <?= $passed_count ?></p>
        <p><strong>Failed:</strong> <?= $total_tests - $passed_count ?></p>
        <p><strong>Success Rate:</strong> <?= round(($passed_count / $total_tests) * 100, 2) ?>%</p>
    </div>
    
    <?php foreach ($test_results as $test): ?>
        <div class="test <?= $test['success'] ? 'success' : 'failure' ?>">
            <h3><?= htmlspecialchars($test['name']) ?> <?= $test['success'] ? '✓' : '✗' ?></h3>
            <p>
                <span class="method"><?= $test['method'] ?></span> 
                <span class="url"><?= htmlspecialchars($test['url']) ?></span>
            </p>
            <p>
                <strong>Expected:</strong> <?= $test['expected_code'] ?> | 
                <strong>Actual:</strong> <?= $test['actual_code'] ?>
            </p>
            <?php if ($test['error']): ?>
                <p><strong>Error:</strong> <?= htmlspecialchars($test['error']) ?></p>
            <?php endif; ?>
            <?php if ($test['response']): ?>
                <div class="response"><?= htmlspecialchars($test['response']) ?></div>
            <?php endif; ?>
        </div>
    <?php endforeach; ?>
    
    <p><em>Generated at: <?= date('Y-m-d H:i:s') ?></em></p>
</body>
</html>
