<?php
/**
 * User Management
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require admin role
requireAdmin();

$current_page = 'users';
$page_title = 'User Management';

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $pdo = getDbConnection();
        
        if ($action === 'toggle_status' && isset($_POST['user_id'])) {
            $user_id = (int)$_POST['user_id'];
            $new_status = (int)$_POST['new_status'];
            
            $stmt = $pdo->prepare("UPDATE users SET status = ? WHERE id = ?");
            $stmt->execute([$new_status, $user_id]);
            
            showAlert('User status updated successfully', 'success');
        }
        
        if ($action === 'delete' && isset($_POST['user_id'])) {
            $user_id = (int)$_POST['user_id'];
            $current_user = getCurrentUser();
            
            if ($user_id === $current_user['id']) {
                throw new Exception('You cannot delete your own account');
            }
            
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            
            showAlert('User deleted successfully', 'success');
        }
        
    } catch (Exception $e) {
        showAlert($e->getMessage(), 'danger');
    }
    
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}

// Get users
try {
    $pdo = getDbConnection();
    
    $stmt = $pdo->query("
        SELECT id, name, email, role, status, last_login, created_at
        FROM users 
        ORDER BY created_at DESC
    ");
    $users = $stmt->fetchAll();
    
    // Get user statistics
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN status = 1 THEN 1 END) as active_users,
            COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
            COUNT(CASE WHEN role = 'staff' THEN 1 END) as staff_users
        FROM users
    ");
    $user_stats = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Users page error: " . $e->getMessage());
    $users = [];
    $user_stats = ['total_users' => 0, 'active_users' => 0, 'admin_users' => 0, 'staff_users' => 0];
}

include 'includes/header.php';
?>

<!-- User Management Content -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">User Management</h1>
        <p class="text-muted">Manage system users and permissions</p>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="bi bi-person-plus me-2"></i>
            Add New User
        </button>
    </div>
</div>

<!-- User Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Users</h6>
                        <h3 class="mb-0"><?= $user_stats['total_users'] ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Active Users</h6>
                        <h3 class="mb-0"><?= $user_stats['active_users'] ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-person-check" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Administrators</h6>
                        <h3 class="mb-0"><?= $user_stats['admin_users'] ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-shield-check" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Staff Users</h6>
                        <h3 class="mb-0"><?= $user_stats['staff_users'] ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-person" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-table me-2"></i>
            System Users
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($users)): ?>
            <div class="text-center py-4">
                <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-2">No users found</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <?= strtoupper(substr($user['name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <div class="fw-medium"><?= htmlspecialchars($user['name']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($user['email']) ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $user['role'] === 'admin' ? 'warning' : 'info' ?>">
                                        <?= ucfirst($user['role']) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $user['status'] ? 'success' : 'danger' ?>">
                                        <?= $user['status'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($user['last_login']): ?>
                                        <small><?= formatDate($user['last_login'], 'M j, Y g:i A') ?></small>
                                    <?php else: ?>
                                        <small class="text-muted">Never</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?= formatDate($user['created_at'], 'M j, Y') ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <?php if ($user['id'] !== getCurrentUser()['id']): ?>
                                            <button type="button" class="btn btn-outline-<?= $user['status'] ? 'warning' : 'success' ?>" 
                                                    onclick="toggleUserStatus(<?= $user['id'] ?>, <?= $user['status'] ? 0 : 1 ?>)" 
                                                    title="<?= $user['status'] ? 'Deactivate' : 'Activate' ?>">
                                                <i class="bi bi-<?= $user['status'] ? 'pause' : 'play' ?>"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteUser(<?= $user['id'] ?>, '<?= htmlspecialchars($user['name']) ?>')" 
                                                    title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        <?php else: ?>
                                            <span class="badge bg-primary">Current User</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="staff">Staff</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Hidden Forms -->
<form id="statusForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="toggle_status">
    <input type="hidden" name="user_id" id="statusUserId">
    <input type="hidden" name="new_status" id="statusNewStatus">
</form>

<form id="deleteUserForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="user_id" id="deleteUserId">
</form>

<?php
$extra_js = "
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}
</style>

<script>
function toggleUserStatus(userId, newStatus) {
    const action = newStatus ? 'activate' : 'deactivate';
    if (confirm('Are you sure you want to ' + action + ' this user?')) {
        document.getElementById('statusUserId').value = userId;
        document.getElementById('statusNewStatus').value = newStatus;
        document.getElementById('statusForm').submit();
    }
}

function deleteUser(userId, userName) {
    if (confirmDelete('Are you sure you want to delete user \"' + userName + '\"? This action cannot be undone.')) {
        document.getElementById('deleteUserId').value = userId;
        document.getElementById('deleteUserForm').submit();
    }
}

document.getElementById('addUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    const submitBtn = this.querySelector('button[type=\"submit\"]');
    const hideLoading = showLoading(submitBtn);
    
    makeApiRequest('auth/register', 'POST', data)
        .then(response => {
            hideLoading();
            if (response.success) {
                showToast('User created successfully', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(response.data?.error?.message || 'Failed to create user', 'danger');
            }
        });
});
</script>
";

include 'includes/footer.php';
?>
