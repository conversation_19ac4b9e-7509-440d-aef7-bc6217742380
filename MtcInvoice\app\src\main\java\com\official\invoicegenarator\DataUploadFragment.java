package com.official.invoicegenarator;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;

// Fragment class for uploading data
public class DataUploadFragment extends Fragment {

    // Model class for the data structure
    static class DataItem {
        private String description;
        private String location;
        private String qr;
        private String lpo;
        private String inb;
        private String amount;
        private String w_a;
        private String paymentStatus; // Renamed for clarity
        private long timestamp; // To store the timestamp

        // Default constructor required for calls to DataSnapshot.getValue(DataItem.class)
        public DataItem() {}

        public DataItem(String description, String location, String qr, String lpo, String inb, String amount, String w_a, String paymentStatus, long timestamp) {
            this.description = description;
            this.location = location;
            this.qr = qr;
            this.lpo = lpo;
            this.inb = inb;
            this.amount = amount;
            this.w_a = w_a;
            this.paymentStatus = paymentStatus;
            this.timestamp = timestamp;
        }

        // Getters and Setters
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }

        public String getQr() { return qr; }
        public void setQr(String qr) { this.qr = qr; }

        public String getLpo() { return lpo; }
        public void setLpo(String lpo) { this.lpo = lpo; }

        public String getInb() { return inb; }
        public void setInb(String inb) { this.inb = inb; }

        public String getAmount() { return amount; }
        public void setAmount(String amount) { this.amount = amount; }

        public String getW_a() { return w_a; }
        public void setW_a(String w_a) { this.w_a = w_a; }

        public String getPaymentStatus() { return paymentStatus; }
        public void setPaymentStatus(String paymentStatus) { this.paymentStatus = paymentStatus; }

        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }

    private EditText editTextDescription, editTextLocation, editTextQR, editTextLPO, editTextINB, editTextAmount, editTextW_A, editTextPaymentStatus;
    private Button buttonUpload;
    private DatabaseReference databaseReference;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_data_upload, container, false);

        // Initialize Firebase Database reference
        databaseReference = FirebaseDatabase.getInstance().getReference("dataItems");

        // Initialize UI components
        editTextDescription = view.findViewById(R.id.editTextDescription);
        editTextLocation = view.findViewById(R.id.editTextLocation);
        editTextQR = view.findViewById(R.id.editTextQR);
        editTextLPO = view.findViewById(R.id.editTextLPO);
        editTextINB = view.findViewById(R.id.editTextINB);
        editTextAmount = view.findViewById(R.id.editTextAmount);
        editTextW_A = view.findViewById(R.id.editTextW_A);
        editTextPaymentStatus = view.findViewById(R.id.payment_status);
        buttonUpload = view.findViewById(R.id.buttonUpload);

        buttonUpload.setOnClickListener(v -> uploadData());

        return view;
    }

    private void uploadData() {
        String description = editTextDescription.getText().toString().trim();
        String location = editTextLocation.getText().toString().trim();
        String qr = editTextQR.getText().toString().trim();
        String lpo = editTextLPO.getText().toString().trim();
        String inb = editTextINB.getText().toString().trim();
        String amount = editTextAmount.getText().toString().trim();
        String w_a = editTextW_A.getText().toString().trim();
        String paymentStatus = editTextPaymentStatus.getText().toString().trim();

        if (!description.isEmpty() && !location.isEmpty() && !qr.isEmpty() && !lpo.isEmpty() && !inb.isEmpty() && !amount.isEmpty() && !w_a.isEmpty() && !paymentStatus.isEmpty()) {

            long timestamp = System.currentTimeMillis(); // Get the current timestamp

            String id = databaseReference.push().getKey();
            DataItem dataItem = new DataItem(description, location, qr, lpo, inb, amount, w_a, paymentStatus, timestamp);

            databaseReference.child(id).setValue(dataItem)
                    .addOnCompleteListener(task -> {
                        if (task.isSuccessful()) {
                            Toast.makeText(getContext(), "Data Uploaded Successfully", Toast.LENGTH_SHORT).show();
                            clearFields();
                        } else {
                            Toast.makeText(getContext(), "Data Upload Failed", Toast.LENGTH_SHORT).show();
                        }
                    });
        } else {
            Toast.makeText(getContext(), "Please fill all fields", Toast.LENGTH_SHORT).show();
        }
    }

    private void clearFields() {
        editTextDescription.setText("");
        editTextLocation.setText("");
        editTextQR.setText("");
        editTextLPO.setText("");
        editTextINB.setText("");
        editTextAmount.setText("");
        editTextW_A.setText("");
        editTextPaymentStatus.setText("");
    }
}