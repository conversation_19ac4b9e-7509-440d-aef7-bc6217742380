# Android App Firebase Migration Plan

This document outlines the complete plan for migrating the MtcInvoice Android app from Firebase to the custom PHP/MySQL backend.

## Current Firebase Usage Analysis

### 🔥 **Firebase Realtime Database**
**Location**: `DataUploadFragment.java`, `DataUploadDialogFragment.java`, `UpdateDeleteFragment.java`

**Current Implementation**:
```java
// Firebase Database Reference
databaseReference = FirebaseDatabase.getInstance().getReference("dataItems");

// Create Operation
String id = databaseReference.push().getKey();
DataItem dataItem = new DataItem(description, location, qr, lpo, inb, amount, w_a, paymentStatus, timestamp);
databaseReference.child(id).setValue(dataItem);

// Read Operation
databaseReference.addValueEventListener(new ValueEventListener() {
    @Override
    public void onDataChange(@NonNull DataSnapshot dataSnapshot) {
        // Process data
    }
});

// Update Operation
databaseReference.child(item.getId()).setValue(updatedItem);

// Delete Operation
databaseReference.child(itemId).removeValue();
```

**Data Structure**:
```java
class DataItem {
    private String description;
    private String location;
    private String qr;
    private String lpo;
    private String inb;
    private String amount;
    private String w_a;
    private String paymentStatus;
    private long timestamp;
}
```

### 🔥 **Firebase Storage**
**Location**: `Home.java`, `InvoiceTwo.java`, `DownloadListActivity.java`

**Current Implementation**:
```java
// Upload to Firebase Storage
FirebaseStorage storage = FirebaseStorage.getInstance();
StorageReference storageRef = storage.getReference("pdfs/" + fileName);
UploadTask uploadTask = storageRef.putFile(pdfUri);

// Download from Firebase Storage
storageRef.getDownloadUrl().addOnSuccessListener(uri -> {
    // Handle download URL
});

// List files
storage.getReference("pdfs/").listAll();

// Delete file
pdfRef.delete();
```

### 📱 **Local SQLite Usage**
**Location**: `expense/DatabaseHelper.java`

**Current Implementation**:
```java
// Already using SQLite for expense tracking
public class DatabaseHelper extends SQLiteOpenHelper {
    private static final String DATABASE_NAME = "ExpenseManager.db";
    private static final String TABLE_TRANSACTIONS = "transactions";
    
    // CRUD operations already implemented
    public void addTransaction(int amount, String category, String date, String note, String type);
    public List<Expense> getExpenseTransactions();
    public List<Income> getIncomeTransactions();
}
```

## Migration Strategy

### 🎯 **Phase 1: HTTP Client Implementation**

#### **1.1 Add HTTP Dependencies**
Add to `app/build.gradle`:
```gradle
implementation 'com.squareup.okhttp3:okhttp:4.12.0'
implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
implementation 'com.google.code.gson:gson:2.10.1' // Already present
```

#### **1.2 Create HTTP Client Utility**
**File**: `app/src/main/java/com/official/invoicegenarator/network/ApiClient.java`

```java
public class ApiClient {
    private static final String BASE_URL = "http://192.168.0.106/MtcInvoiceMasudvi/api/";
    private static OkHttpClient httpClient;
    private static Gson gson;
    
    // HTTP methods: GET, POST, PUT, DELETE
    // Authentication handling
    // Response parsing
    // Error handling
}
```

#### **1.3 Create API Service Interface**
**File**: `app/src/main/java/com/official/invoicegenarator/network/ApiService.java`

```java
public interface ApiService {
    // Authentication
    void login(String email, String password, ApiCallback<LoginResponse> callback);
    
    // Invoice Data Operations
    void createInvoiceItem(DataItem item, ApiCallback<CreateResponse> callback);
    void getInvoiceItems(ApiCallback<List<DataItem>> callback);
    void updateInvoiceItem(String id, DataItem item, ApiCallback<UpdateResponse> callback);
    void deleteInvoiceItem(String id, ApiCallback<DeleteResponse> callback);
    
    // File Operations
    void uploadFile(File file, ApiCallback<UploadResponse> callback);
    void downloadFile(String filename, ApiCallback<File> callback);
    void listFiles(ApiCallback<List<FileInfo>> callback);
    void deleteFile(String filename, ApiCallback<DeleteResponse> callback);
}
```

### 🎯 **Phase 2: Local SQLite Extension**

#### **2.1 Extend DatabaseHelper**
**File**: `app/src/main/java/com/official/invoicegenarator/database/InvoiceDataHelper.java`

```java
public class InvoiceDataHelper extends SQLiteOpenHelper {
    private static final String DATABASE_NAME = "MtcInvoice.db";
    private static final int DATABASE_VERSION = 1;
    
    // Tables
    private static final String TABLE_INVOICE_DATA = "invoice_data_items";
    private static final String TABLE_FILES = "files";
    private static final String TABLE_SYNC_STATUS = "sync_status";
    
    // Invoice Data CRUD operations
    // File metadata storage
    // Sync status tracking
    // Offline support
}
```

#### **2.2 Create Data Models**
**File**: `app/src/main/java/com/official/invoicegenarator/models/`

```java
// InvoiceDataItem.java - Enhanced with sync status
public class InvoiceDataItem {
    private String id;
    private String description;
    private String location;
    private String qr;
    private String lpo;
    private String inb;
    private String amount;
    private String w_a;
    private String paymentStatus;
    private long timestamp;
    private String currentDateTime;
    
    // Sync fields
    private boolean isSynced;
    private long lastSyncTime;
    private String syncStatus; // "pending", "synced", "error"
}

// FileInfo.java - File metadata
public class FileInfo {
    private String id;
    private String originalName;
    private String storedName;
    private String localPath;
    private String remotePath;
    private long fileSize;
    private String fileType;
    private boolean isDownloaded;
    private boolean isUploaded;
    private long uploadTime;
}
```

### 🎯 **Phase 3: Repository Pattern Implementation**

#### **3.1 Create Repository Classes**
**File**: `app/src/main/java/com/official/invoicegenarator/repository/InvoiceRepository.java`

```java
public class InvoiceRepository {
    private InvoiceDataHelper localDb;
    private ApiService apiService;
    private NetworkUtils networkUtils;
    
    // Hybrid operations (local + remote)
    public void createInvoiceItem(DataItem item, RepositoryCallback<DataItem> callback);
    public void getInvoiceItems(boolean forceRefresh, RepositoryCallback<List<DataItem>> callback);
    public void updateInvoiceItem(String id, DataItem item, RepositoryCallback<DataItem> callback);
    public void deleteInvoiceItem(String id, RepositoryCallback<Void> callback);
    
    // Sync operations
    public void syncPendingChanges();
    public void syncFromServer();
}
```

#### **3.2 Create File Repository**
**File**: `app/src/main/java/com/official/invoicegenarator/repository/FileRepository.java`

```java
public class FileRepository {
    private InvoiceDataHelper localDb;
    private ApiService apiService;
    private File localStorageDir;
    
    // File operations
    public void uploadFile(File file, RepositoryCallback<FileInfo> callback);
    public void downloadFile(String filename, RepositoryCallback<File> callback);
    public void listFiles(boolean includeLocal, RepositoryCallback<List<FileInfo>> callback);
    public void deleteFile(String filename, RepositoryCallback<Void> callback);
    
    // Local file management
    public File getLocalFile(String filename);
    public void cleanupOldFiles();
}
```

### 🎯 **Phase 4: UI Layer Migration**

#### **4.1 Update Fragment Classes**
Replace Firebase operations with Repository calls:

**DataUploadFragment.java**:
```java
// Before (Firebase)
databaseReference.child(id).setValue(dataItem);

// After (Repository)
invoiceRepository.createInvoiceItem(dataItem, new RepositoryCallback<DataItem>() {
    @Override
    public void onSuccess(DataItem result) {
        // Handle success
    }
    
    @Override
    public void onError(String error) {
        // Handle error
    }
});
```

**UpdateDeleteFragment.java**:
```java
// Before (Firebase)
databaseReference.addValueEventListener(new ValueEventListener() { ... });

// After (Repository)
invoiceRepository.getInvoiceItems(false, new RepositoryCallback<List<DataItem>>() {
    @Override
    public void onSuccess(List<DataItem> items) {
        // Update UI
    }
});
```

#### **4.2 Update File Operations**
**Home.java** and **InvoiceTwo.java**:
```java
// Before (Firebase Storage)
storageRef.putFile(pdfUri);

// After (File Repository)
fileRepository.uploadFile(pdfFile, new RepositoryCallback<FileInfo>() {
    @Override
    public void onSuccess(FileInfo fileInfo) {
        // Handle success
    }
});
```

### 🎯 **Phase 5: Authentication Integration**

#### **5.1 Create Authentication Manager**
**File**: `app/src/main/java/com/official/invoicegenarator/auth/AuthManager.java`

```java
public class AuthManager {
    private SharedPreferences prefs;
    private ApiService apiService;
    
    // Authentication methods
    public void login(String email, String password, AuthCallback callback);
    public void logout();
    public boolean isLoggedIn();
    public String getAuthToken();
    public void refreshToken();
    
    // User management
    public User getCurrentUser();
    public void updateUserProfile(User user, AuthCallback callback);
}
```

#### **5.2 Add Authentication to API Calls**
```java
public class ApiClient {
    private String authToken;
    
    private Request.Builder addAuthHeaders(Request.Builder builder) {
        if (authToken != null) {
            builder.addHeader("Authorization", "Bearer " + authToken);
        }
        return builder;
    }
}
```

### 🎯 **Phase 6: Offline Support**

#### **6.1 Sync Manager**
**File**: `app/src/main/java/com/official/invoicegenarator/sync/SyncManager.java`

```java
public class SyncManager {
    private InvoiceRepository invoiceRepository;
    private FileRepository fileRepository;
    private NetworkUtils networkUtils;
    
    // Sync operations
    public void performFullSync();
    public void syncPendingChanges();
    public void schedulePeriodicSync();
    
    // Conflict resolution
    public void resolveConflicts();
}
```

#### **6.2 Network State Monitoring**
```java
public class NetworkUtils {
    public boolean isNetworkAvailable();
    public void registerNetworkCallback(NetworkCallback callback);
    public void unregisterNetworkCallback();
}
```

## Implementation Steps

### 📋 **Step 1: Prepare Infrastructure**
1. Add HTTP dependencies to `build.gradle`
2. Create network package structure
3. Implement `ApiClient` and `ApiService`
4. Create data models with sync fields

### 📋 **Step 2: Extend Local Database**
1. Create `InvoiceDataHelper` class
2. Design local database schema
3. Implement CRUD operations
4. Add sync status tracking

### 📋 **Step 3: Implement Repository Pattern**
1. Create `InvoiceRepository` class
2. Create `FileRepository` class
3. Implement hybrid operations (local + remote)
4. Add error handling and retry logic

### 📋 **Step 4: Update UI Components**
1. Replace Firebase calls in `DataUploadFragment`
2. Update `UpdateDeleteFragment` with repository calls
3. Modify file operations in `Home.java` and `InvoiceTwo.java`
4. Update `DownloadListActivity` for new file system

### 📋 **Step 5: Add Authentication**
1. Create `AuthManager` class
2. Implement login/logout functionality
3. Add token management
4. Update API calls with authentication

### 📋 **Step 6: Implement Sync System**
1. Create `SyncManager` class
2. Add background sync service
3. Implement conflict resolution
4. Add network state monitoring

### 📋 **Step 7: Testing and Validation**
1. Test all CRUD operations
2. Verify file upload/download
3. Test offline functionality
4. Validate data integrity
5. Performance testing

## Data Migration Strategy

### 🔄 **Firebase to Local Database**
1. **Export Firebase Data**: Use Firebase Admin SDK or manual export
2. **Data Transformation**: Convert Firebase format to local SQLite format
3. **Import Script**: Create utility to import existing data
4. **Validation**: Verify data integrity after migration

### 🔄 **File Migration**
1. **Download Files**: Batch download all files from Firebase Storage
2. **Local Storage**: Store files in app's private storage
3. **Metadata Update**: Update file references in database
4. **Cleanup**: Remove Firebase Storage references

## Testing Strategy

### 🧪 **Unit Tests**
- Repository classes
- API client functionality
- Data model validation
- Sync logic

### 🧪 **Integration Tests**
- API communication
- Database operations
- File operations
- Authentication flow

### 🧪 **UI Tests**
- Fragment functionality
- User interactions
- Error handling
- Offline scenarios

## Rollback Plan

### 🔙 **Gradual Migration**
1. **Feature Flags**: Use flags to switch between Firebase and new system
2. **Parallel Operation**: Run both systems simultaneously during transition
3. **Monitoring**: Monitor performance and error rates
4. **Quick Rollback**: Ability to revert to Firebase if issues arise

### 🔙 **Data Backup**
1. **Firebase Backup**: Maintain Firebase data during transition
2. **Local Backup**: Regular backups of local database
3. **File Backup**: Backup of local files
4. **Recovery Procedures**: Documented recovery steps

## Performance Considerations

### ⚡ **Optimization Strategies**
1. **Lazy Loading**: Load data on demand
2. **Caching**: Cache frequently accessed data
3. **Pagination**: Implement pagination for large datasets
4. **Background Sync**: Sync data in background threads
5. **Compression**: Compress large files before upload

### ⚡ **Memory Management**
1. **Efficient Queries**: Optimize database queries
2. **Image Optimization**: Compress images before storage
3. **Cache Management**: Implement LRU cache for images
4. **Memory Leaks**: Prevent memory leaks in async operations

This migration plan ensures a smooth transition from Firebase to the custom backend while maintaining all existing functionality and improving offline capabilities.
