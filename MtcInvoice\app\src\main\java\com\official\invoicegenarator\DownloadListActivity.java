package com.official.invoicegenarator;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.documentfile.provider.DocumentFile;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.Tasks;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.firebase.storage.FileDownloadTask;
import com.google.firebase.storage.FirebaseStorage;
import com.google.firebase.storage.OnProgressListener;
import com.google.firebase.storage.StorageMetadata;
import com.google.firebase.storage.StorageReference;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class DownloadListActivity extends AppCompatActivity {

    private static final int REQUEST_CODE_OPEN_DOCUMENT_TREE = 42;
    private RecyclerView recyclerView;
    private PdfAdapter pdfAdapter;
    private List<StorageReference> pdfList;
    private List<StorageReference> filteredPdfList; // For filtering
    private FirebaseStorage storage;
    private Uri selectedFolderUri;
    private ProgressBar loadingProgressBar;
    private ProgressDialog progressDialog;
    private EditText searchEditText; // Search EditText
    private RadioGroup sortOptions; // RadioGroup for sort options

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_download_list);

        initializeViews();
        initializeFirebaseStorage();
        setupSortOptions(); // Set up sorting options
        fetchPdfFiles();
    }

    private void initializeViews() {
        recyclerView = findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        pdfList = new ArrayList<>();
        filteredPdfList = new ArrayList<>();
        pdfAdapter = new PdfAdapter(this, filteredPdfList);
        recyclerView.setAdapter(pdfAdapter);
        loadingProgressBar = findViewById(R.id.loadingProgressBar);
        searchEditText = findViewById(R.id.searchEditText);
        sortOptions = findViewById(R.id.sortOptions); // Initialize sort options

        // Add search functionality
        searchEditText.addTextChangedListener(new android.text.TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                filterPdfList(s.toString());
            }

            @Override
            public void afterTextChanged(android.text.Editable s) {}
        });
    }

    private void initializeFirebaseStorage() {
        storage = FirebaseStorage.getInstance();
    }

    private void setupSortOptions() {
        sortOptions.setOnCheckedChangeListener((group, checkedId) -> {
            fetchPdfFiles(); // Fetch PDF files whenever the sort option changes
        });
    }
    public class PdfItem {
        StorageReference pdfRef;
        StorageMetadata metadata;

        public PdfItem(StorageReference pdfRef, StorageMetadata metadata) {
            this.pdfRef = pdfRef;
            this.metadata = metadata;
        }
    }

    private void fetchPdfFiles() {
        loadingProgressBar.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.GONE);

        StorageReference storageRef = storage.getReference().child("pdfs/");
        storageRef.listAll().addOnSuccessListener(listResult -> {
            pdfList.clear();
            List<PdfItem> pdfItems = new ArrayList<>();

            for (StorageReference fileRef : listResult.getItems()) {
                if (fileRef.getName().endsWith(".pdf")) {
                    pdfList.add(fileRef);
                    pdfItems.add(new PdfItem(fileRef, null)); // Initialize with null metadata
                }
            }

            // Fetch metadata for each file to get creation time and sort
            List<Task<StorageMetadata>> metadataTasks = new ArrayList<>();
            for (StorageReference pdfRef : pdfList) {
                metadataTasks.add(pdfRef.getMetadata());
            }

            // Wait for all metadata tasks to complete
            Tasks.whenAllSuccess(metadataTasks).addOnSuccessListener(tasks -> {
                for (int i = 0; i < tasks.size(); i++) {
                    pdfItems.get(i).metadata = (StorageMetadata) tasks.get(i); // Store metadata
                }

                // Sort the pdfItems based on the selected sort option
                if (sortOptions.getCheckedRadioButtonId() == R.id.sortNewest) {
                    Collections.sort(pdfItems, (item1, item2) -> {
                        long timestamp1 = item1.metadata.getCreationTimeMillis();
                        long timestamp2 = item2.metadata.getCreationTimeMillis();
                        return Long.compare(timestamp2, timestamp1); // Newest first
                    });
                } else {
                    Collections.sort(pdfItems, (item1, item2) -> {
                        long timestamp1 = item1.metadata.getCreationTimeMillis();
                        long timestamp2 = item2.metadata.getCreationTimeMillis();
                        return Long.compare(timestamp1, timestamp2); // Oldest first
                    });
                }

                // Clear and update filtered list
                filteredPdfList.clear();
                for (PdfItem item : pdfItems) {
                    filteredPdfList.add(item.pdfRef); // Update filtered list with pdfRef
                }
                pdfAdapter.notifyDataSetChanged();
                loadingProgressBar.setVisibility(View.GONE);
                recyclerView.setVisibility(View.VISIBLE);
            });

        }).addOnFailureListener(e -> {
            Toast.makeText(this, "Error fetching PDF files", Toast.LENGTH_SHORT).show();
            loadingProgressBar.setVisibility(View.GONE);
        });
    }

    /*private void fetchPdfFiles() {
        loadingProgressBar.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.GONE);

        StorageReference storageRef = storage.getReference().child("pdfs/");
        storageRef.listAll().addOnSuccessListener(listResult -> {
            pdfList.clear();
            for (StorageReference fileRef : listResult.getItems()) {
                if (fileRef.getName().endsWith(".pdf")) {
                    pdfList.add(fileRef);
                }
            }

            // Check selected sort option
            if (sortOptions.getCheckedRadioButtonId() == R.id.sortNewest) {
                Collections.reverse(pdfList); // Show newest first
            }

            filteredPdfList.clear();
            filteredPdfList.addAll(pdfList);
            pdfAdapter.notifyDataSetChanged();
            loadingProgressBar.setVisibility(View.GONE);
            recyclerView.setVisibility(View.VISIBLE);
        }).addOnFailureListener(e -> {
            Toast.makeText(this, "Error fetching PDF files", Toast.LENGTH_SHORT).show();
            loadingProgressBar.setVisibility(View.GONE);
        });
    }*/

    private void filterPdfList(String query) {
        filteredPdfList.clear();
        if (query.isEmpty()) {
            filteredPdfList.addAll(pdfList); // Show all if query is empty
        } else {
            for (StorageReference pdfRef : pdfList) {
                if (pdfRef.getName().toLowerCase().contains(query.toLowerCase())) {
                    filteredPdfList.add(pdfRef);
                }
            }
        }
        pdfAdapter.notifyDataSetChanged(); // Notify adapter for changes
    }

    private void openFolderPicker() {
        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT_TREE);
        intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
        startActivityForResult(intent, REQUEST_CODE_OPEN_DOCUMENT_TREE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_OPEN_DOCUMENT_TREE && resultCode == RESULT_OK && data != null) {
            selectedFolderUri = data.getData();
            Toast.makeText(this, "Folder selected: " + selectedFolderUri.toString(), Toast.LENGTH_SHORT).show();
        }
    }

    public class PdfAdapter extends RecyclerView.Adapter<PdfAdapter.PdfViewHolder> {

        private Context context;
        private List<StorageReference> pdfList;

        public PdfAdapter(Context context, List<StorageReference> pdfList) {
            this.context = context;
            this.pdfList = pdfList;
        }

        @NonNull
        @Override
        public PdfViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(context).inflate(R.layout.pdf_item, parent, false);
            return new PdfViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull PdfViewHolder holder, int position) {
            StorageReference pdfRef = pdfList.get(position);
            holder.pdfName.setText(pdfRef.getName());

            // Fetch and format the timestamp
            pdfRef.getMetadata().addOnSuccessListener(storageMetadata -> {
                long timestamp = storageMetadata.getCreationTimeMillis();
                String formattedDate = formatDate(timestamp);
                holder.pdfTimestamp.setText(formattedDate); // Set formatted date

                // Get the file size and format it
                long fileSize = storageMetadata.getSizeBytes();
                holder.pdfSize.setText(formatFileSize(fileSize)); // Set the file size
            }).addOnFailureListener(e -> {
                holder.pdfTimestamp.setText("Unknown date"); // Handle error
                holder.pdfSize.setText("Unknown size"); // Handle error for size
            });

            // Set up the download button action
            holder.downloadButton.setOnClickListener(v -> {
                if (selectedFolderUri == null) {
                    openFolderPicker(); // Ask user to select a folder
                } else {
                    showSaveDialog(pdfRef); // Show dialog to enter the filename
                }
            });

            // Set up the view button action
            holder.viewButton.setOnClickListener(v -> {
                viewPdf(pdfRef);
            });

            // Set up the delete button action
            holder.deleteButton.setOnClickListener(v -> {
                deletePdf(pdfRef);
            });
            // Apply animation to the item view
            setAnimation(holder.itemView);
        }
        private void setAnimation(View view) {
            Animation animation = AnimationUtils.loadAnimation(view.getContext(), android.R.anim.slide_in_left);
            view.startAnimation(animation);
        }
        @Override
        public int getItemCount() {
            return pdfList.size();
        }

        public class PdfViewHolder extends RecyclerView.ViewHolder {
            TextView pdfName;
            TextView pdfTimestamp; // Timestamp TextView
            TextView pdfSize; // File size TextView
            ImageView downloadButton;
            ImageView viewButton;
            ImageView deleteButton;

            public PdfViewHolder(@NonNull View itemView) {
                super(itemView);
                pdfName = itemView.findViewById(R.id.pdfName);
                pdfTimestamp = itemView.findViewById(R.id.pdfTimestamp); // Initialize timestamp TextView
                pdfSize = itemView.findViewById(R.id.pdfSize); // Initialize file size TextView
                downloadButton = itemView.findViewById(R.id.downloadButton);
                viewButton = itemView.findViewById(R.id.viewButton);
                deleteButton = itemView.findViewById(R.id.deleteButton);
            }
        }
        // Method to format the date and time
        private String formatDate(long timestamp) {
            Date date = new Date(timestamp);
            String dateFormat = "hh:mm a"; // 12-hour format
            String timeFormat = "MMMM dd, yyyy"; // Full date format
            String timeString = android.text.format.DateFormat.format(dateFormat, date).toString();
            String dateString = android.text.format.DateFormat.format(timeFormat, date).toString();
            return timeString + "\n" + dateString; // Return formatted string with new line
        }

        private void showSaveDialog(StorageReference pdfRef) {
            LayoutInflater inflater = LayoutInflater.from(context);
            View dialogView = inflater.inflate(R.layout.dialog_save_pdf, null);
            final EditText input = dialogView.findViewById(R.id.fileNameInput);

            MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(context);
            builder.setTitle("Save PDF")
                    .setView(dialogView)
                    .setPositiveButton("Save", (dialog, which) -> {
                        String fileName = input.getText().toString().trim();
                        if (fileName.isEmpty()) {
                            Toast.makeText(context, "File name cannot be empty", Toast.LENGTH_SHORT).show();
                            return;
                        }
                        if (!fileName.endsWith(".pdf")) {
                            fileName += ".pdf";
                        }
                        savePdf(pdfRef, fileName);
                    })
                    .setNegativeButton("Cancel", (dialog, which) -> dialog.cancel())
                    .show();
        }

        private void savePdf(StorageReference pdfRef, String fileName) {
            if (selectedFolderUri == null) {
                Toast.makeText(context, "Please select a folder first.", Toast.LENGTH_SHORT).show();
                return;
            }

            showProgressDialog(); // Show progress dialog

            try {
                DocumentFile folder = DocumentFile.fromTreeUri(context, selectedFolderUri);
                DocumentFile newFile = folder.createFile("application/pdf", fileName);
                File localFile = new File(getCacheDir(), fileName);

                pdfRef.getFile(localFile).addOnSuccessListener(taskSnapshot -> {
                    try {
                        OutputStream outputStream = context.getContentResolver().openOutputStream(newFile.getUri());
                        FileInputStream fileInputStream = new FileInputStream(localFile);
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = fileInputStream.read(buffer)) > 0) {
                            outputStream.write(buffer, 0, length);
                        }
                        outputStream.flush();
                        outputStream.close();
                        fileInputStream.close();
                        Toast.makeText(context, "PDF saved successfully", Toast.LENGTH_SHORT).show();
                    } catch (IOException e) {
                        Toast.makeText(context, "Error saving PDF", Toast.LENGTH_SHORT).show();
                    } finally {
                        hideProgressDialog(); // Hide progress dialog
                    }
                }).addOnFailureListener(e -> {
                    Toast.makeText(context, "Error downloading PDF", Toast.LENGTH_SHORT).show();
                    hideProgressDialog(); // Hide progress dialog in case of failure
                }).addOnProgressListener(new OnProgressListener<FileDownloadTask.TaskSnapshot>() {
                    @Override
                    public void onProgress(FileDownloadTask.TaskSnapshot taskSnapshot) {
                        int progress = (int) (100 * taskSnapshot.getBytesTransferred() / taskSnapshot.getTotalByteCount());
                        // Update the progress dialog or UI
                        progressDialog.setMessage("Downloading... " + progress + "%");
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
                Toast.makeText(context, "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                hideProgressDialog(); // Ensure the dialog is hidden on error
            }
        }

        private String formatFileSize(long sizeInBytes) {
            if (sizeInBytes < 1024) {
                return sizeInBytes + " Bytes";
            } else if (sizeInBytes < 1048576) {
                return String.format("%.2f KB", sizeInBytes / 1024.0);
            } else {
                return String.format("%.2f MB", sizeInBytes / 1048576.0);
            }
        }

        private void viewPdf(StorageReference pdfRef) {
            pdfRef.getDownloadUrl().addOnSuccessListener(uri -> {
                Intent intent = new Intent(Intent.ACTION_VIEW);
                intent.setDataAndType(uri, "application/pdf");
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                context.startActivity(intent);
            }).addOnFailureListener(e -> {
                Toast.makeText(context, "Error getting download URL", Toast.LENGTH_SHORT).show();
            });
        }

        private void deletePdf(StorageReference pdfRef) {
            new MaterialAlertDialogBuilder(context)
                    .setTitle("Delete PDF")
                    .setMessage("Are you sure you want to delete this PDF?")
                    .setPositiveButton("Delete", (dialog, which) -> {
                        pdfRef.delete().addOnSuccessListener(aVoid -> {
                            Toast.makeText(context, "PDF deleted successfully", Toast.LENGTH_SHORT).show();
                            fetchPdfFiles(); // Refresh the list
                        }).addOnFailureListener(e -> {
                            Toast.makeText(context, "Error deleting PDF", Toast.LENGTH_SHORT).show();
                        });
                    })
                    .setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss())
                    .show();
        }


        private void showProgressDialog() {
            progressDialog = new ProgressDialog(context);
            progressDialog.setMessage("Please wait...");
            progressDialog.setCancelable(false);
            progressDialog.show();
        }

        private void hideProgressDialog() {
            if (progressDialog != null && progressDialog.isShowing()) {
                progressDialog.dismiss();
            }
        }
    }
}
