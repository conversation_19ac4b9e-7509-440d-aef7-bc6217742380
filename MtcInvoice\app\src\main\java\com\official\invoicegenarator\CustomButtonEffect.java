package com.official.invoicegenarator;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.TimeInterpolator;
import android.graphics.Rect;
import android.media.AudioManager;
import android.media.SoundPool;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;

public class CustomButtonEffect {

    public static final float DEFAULT_PUSH_SCALE = 1.96f;
    public static final long DEFAULT_PUSH_DURATION = 50;
    public static final long DEFAULT_RELEASE_DURATION = 125;
    public static final AccelerateDecelerateInterpolator DEFAULT_INTERPOLATOR
            = new AccelerateDecelerateInterpolator();

    private final View view;
    private final float defaultScale;
    private float pushScale = DEFAULT_PUSH_SCALE;
    private long durationPush = DEFAULT_PUSH_DURATION;
    private long durationRelease = DEFAULT_RELEASE_DURATION;
    private AccelerateDecelerateInterpolator interpolatorPush = DEFAULT_INTERPOLATOR;
    private AccelerateDecelerateInterpolator interpolatorRelease = DEFAULT_INTERPOLATOR;

    private Rect rect;
    private boolean isOutSide;

    private SoundPool soundPool;
    private int soundId;

    public CustomButtonEffect(final View view) {
        this.view = view;
        this.defaultScale = view.getScaleX();

        view.setClickable(true);
        view.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent motionEvent) {
                handleTouchEvent(motionEvent);
                return false;
            }
        });

       // soundPool = new SoundPool(1, AudioManager.STREAM_MUSIC, 0);
       // soundId = soundPool.load(view.getContext(), R.raw.click_sound, 1);
    }

    private void handleTouchEvent(MotionEvent motionEvent) {
        int action = motionEvent.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                isOutSide = false;
                rect = new Rect(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());
                animateScale(pushScale, durationPush, interpolatorPush);
             //   playClickSound();
                break;
            case MotionEvent.ACTION_MOVE:
                if (!isOutSide && rect != null && !rect.contains(view.getLeft() + (int) motionEvent.getX(), view.getTop() + (int) motionEvent.getY())) {
                    isOutSide = true;
                    animateScale(defaultScale, durationRelease, interpolatorRelease);
                }
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                animateScale(defaultScale, durationRelease, interpolatorRelease);
                break;
        }
    }

    private void animateScale(float scale, long duration, TimeInterpolator interpolator) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", scale);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", scale);
        scaleX.setInterpolator(interpolator);
        scaleX.setDuration(duration);
        scaleY.setInterpolator(interpolator);
        scaleY.setDuration(duration);

        final AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.play(scaleX).with(scaleY);
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
            }
        });
        animatorSet.start();
    }

    private void playClickSound() {
        soundPool.play(soundId, 1.0f, 1.0f, 0, 0, 1.0f);
    }

    public CustomButtonEffect setPushScale(float pushScale) {
        this.pushScale = pushScale;
        return this;
    }

    public CustomButtonEffect setDurationPush(long durationPush) {
        this.durationPush = durationPush;
        return this;
    }

    public CustomButtonEffect setDurationRelease(long durationRelease) {
        this.durationRelease = durationRelease;
        return this;
    }

    public CustomButtonEffect setInterpolatorPush(AccelerateDecelerateInterpolator interpolatorPush) {
        this.interpolatorPush = interpolatorPush;
        return this;
    }

    public CustomButtonEffect setInterpolatorRelease(AccelerateDecelerateInterpolator interpolatorRelease) {
        this.interpolatorRelease = interpolatorRelease;
        return this;
    }
}