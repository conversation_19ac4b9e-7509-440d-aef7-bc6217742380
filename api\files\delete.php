<?php
/**
 * File Delete API
 * Delete uploaded files (replaces Firebase Storage delete functionality)
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';
require_once '../utils/jwt.php';

// Only allow DELETE requests
if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
    sendErrorResponse('Method not allowed', 405);
}

try {
    // Authentication required
    $user = JWT::requireAuth(JWT_SECRET_KEY);
    
    // Get filename from query parameter
    $filename = $_GET['file'] ?? null;
    
    if (!$filename) {
        sendErrorResponse('Filename is required', 400);
    }
    
    // Sanitize filename to prevent directory traversal
    $filename = basename($filename);
    
    // Construct file path
    $file_path = UPLOAD_DIR . 'pdfs/' . $filename;
    
    // Check if file exists
    if (!file_exists($file_path)) {
        sendErrorResponse('File not found', 404);
    }
    
    // Get file info before deletion
    $file_size = filesize($file_path);
    
    // Delete file
    if (!unlink($file_path)) {
        sendErrorResponse('Failed to delete file', 500);
    }
    
    // Log deletion activity
    logActivity('file_deleted', [
        'filename' => $filename,
        'file_size' => $file_size,
        'user_id' => $user['user_id']
    ]);
    
    sendSuccessResponse(null, 'File deleted successfully');
    
} catch (Exception $e) {
    error_log("File delete error: " . $e->getMessage());
    sendErrorResponse('File deletion failed', 500);
}
