package com.official.invoicegenarator;

import static com.official.invoicegenarator.R.drawable.fingerprinttwo;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.biometric.BiometricManager;
import androidx.biometric.BiometricPrompt;
import androidx.core.content.ContextCompat;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import java.util.concurrent.Executor;

public class VarifyActivity extends AppCompatActivity {
    private static final int REQUEST_CODE = 100;
    public TextView textView;
    public ImageView imageView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_varify);
        imageView= findViewById(R.id.imageView);
        textView = findViewById(R.id.text_view);
        verify();
       /* imageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
               //verify();
            }
        });*/


    }

    private void verify() {
        BiometricManager biometricManager = BiometricManager.from(this);
        int i = biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG);
        if (i == BiometricManager.BIOMETRIC_SUCCESS) {// Biometric authentication is available
            Executor executor = ContextCompat.getMainExecutor(this);
            BiometricPrompt biometricPrompt = new BiometricPrompt(this, executor, new BiometricPrompt.AuthenticationCallback() {
                @Override
                public void onAuthenticationError(int errorCode, CharSequence errString) {
                    super.onAuthenticationError(errorCode, errString);
                    textView.setText("Authentication error: " + errString);
                    imageView.setImageResource(R.drawable.fingerprinttwo);
                }

                @Override
                public void onAuthenticationSucceeded(@NonNull BiometricPrompt.AuthenticationResult result) {
                    super.onAuthenticationSucceeded(result);
                    imageView.setImageResource(R.drawable.fingerprint);
                    Intent intent = new Intent(VarifyActivity.this, SelectionActivity.class);
                    startActivity(intent);
                    finish();
                }

                @Override
                public void onAuthenticationFailed() {
                    super.onAuthenticationFailed();
                    textView.setText("Authentication failed.");
                    imageView.setImageResource(R.drawable.fingerprinttwo);
                }
            });

            BiometricPrompt.PromptInfo promptInfo = new BiometricPrompt.PromptInfo.Builder()
                    .setTitle("Biometric Authentication")
                    .setSubtitle("Use your biometric credential to authenticate")
                    .setNegativeButtonText("Cancel")
                    .build();

            biometricPrompt.authenticate(promptInfo);
        } else if (i == BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE) {// Biometric authentication is not available on this device
            textView.setText("Biometric authentication is not available on this device.");
        } else if (i == BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE) {// Biometric authentication is currently unavailable
            textView.setText("Biometric authentication is currently unavailable.");
        } else if (i == BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED) {// The user has not enrolled any biometric credentials
            final Intent enrollIntent = new Intent(Settings.ACTION_BIOMETRIC_ENROLL);
            enrollIntent.putExtra(Settings.EXTRA_BIOMETRIC_AUTHENTICATORS_ALLOWED, BiometricManager.Authenticators.BIOMETRIC_STRONG);
            startActivityForResult(enrollIntent, REQUEST_CODE);
        }
    }


}