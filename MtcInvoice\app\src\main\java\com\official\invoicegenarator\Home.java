package com.official.invoicegenarator;

import android.Manifest;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.pdf.PdfDocument;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.StrictMode;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ZoomControls;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.documentfile.provider.DocumentFile;

import com.bumptech.glide.Glide;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.firebase.FirebaseApp;
import com.google.firebase.storage.FirebaseStorage;
import com.google.firebase.storage.OnProgressListener;
import com.google.firebase.storage.StorageReference;
import com.google.firebase.storage.UploadTask;

import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class Home extends AppCompatActivity {
    private static final int REQUEST_PERMISSION_CODE = 1100;
    private static final int REQUEST_PERMISSION_CODE_ANDROID_R = 2296;

    private static final String SHARED_PREF_NAME = "SignaturePrefs";
    private static final String SIGNATURE_URI_KEY = "signature_uri";

    private static final int REQUEST_PDF_SELECT = 2;
    private static final int PICK_IMAGE_REQUEST = 1;
    private static final int PICK_SIGNATURE_IMAGE = 100;
    String fName = String.valueOf(System.currentTimeMillis());
    private LinearLayout tableContainer,banklay;
    private LinearLayout vat_layout;
    private boolean isFirstTable = true;
    private boolean isVatTable = true;
    CustomButtonEffect customButtonEffect;

    private ImageView btnAddTable,btnRemoveTable,add_vat_column,remove_vat_column,add_back_layout,remove_back_layout;
    private int serialNumber = 1; // Initialize serial number to 1
    private EditText date;
    EditText subjectEditText,edit_text;

    // Initialize variables for zoom and pan
    private float scaleFactor = 1.0f;
    private ScaleGestureDetector scaleGestureDetector;
    private float translationX = 0f, translationY = 0f;
    private float previousX = 0f, previousY = 0f;
    private boolean isScaling = false; // To differentiate between zoom and pan
    private LinearLayout sllayout;
    ImageView resetZoomBtn;
    ImageView downloadlist;

    private LinearLayout signatureLayout;
    private ImageView signatureImage;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home);
        FirebaseApp.initializeApp(this);
        if (!checkPermission()) {
            requestPermission();
        }

        StrictMode.VmPolicy.Builder builder = new StrictMode.VmPolicy.Builder();
        StrictMode.setVmPolicy(builder.build());
        MaterialToolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        downloadlist=findViewById(R.id.downloads);
        tableContainer = findViewById(R.id.table_container);
        vat_layout = findViewById(R.id.vat_layout);
        banklay = findViewById(R.id.banklay);
        btnAddTable=findViewById(R.id.add_column);
        btnRemoveTable=findViewById(R.id.remove_column);
        add_vat_column=findViewById(R.id.add_vat_column);
        remove_vat_column=findViewById(R.id.remove_vat_column);
        add_back_layout=findViewById(R.id.add_back_layout);
        remove_back_layout=findViewById(R.id.remove_back_layout);
        edit_text=findViewById(R.id.edit_text);
        date=findViewById(R.id.date);
        subjectEditText = findViewById(R.id.subject);
        customButtonEffect = new CustomButtonEffect(btnAddTable);
        customButtonEffect = new CustomButtonEffect(btnRemoveTable);
        customButtonEffect = new CustomButtonEffect(add_vat_column);
        customButtonEffect = new CustomButtonEffect(remove_vat_column);
        customButtonEffect = new CustomButtonEffect(add_back_layout);
        customButtonEffect = new CustomButtonEffect(remove_back_layout);
        resetZoomBtn = findViewById(R.id.resetZoomBtn);
// Get the current date
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
        SimpleDateFormat newsdf = new SimpleDateFormat("ddMMyyyy", Locale.getDefault());
        String currentDate = sdf.format(new Date());
        String newcurrentDate = newsdf.format(new Date());

// Set the current date to the EditText
        date.setText("Date: "+currentDate);

// Set QR Text with current date
        EditText qrTextView = findViewById(R.id.qrTextView);
      //  String formattedDate = new SimpleDateFormat("yyMMdd", Locale.getDefault()).format(new Date());
        qrTextView.setText("QR No : QR " + newcurrentDate + "SIB");

        findViewById(R.id.downloadBtn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                EditText subjectEditText = findViewById(R.id.subject);  // Get the EditText reference
                String subjectText = subjectEditText.getText().toString().trim();

                // Check if the EditText is empty or still has the placeholder text
                if (subjectText.isEmpty() || subjectText.equals("SUBJECT:")) {
                    // Show a dialog if the input is empty
                    showAlertDialog();
                } else {
                    // Call layoutToPdfConverter if input is valid
                    layoutToPdfConverter();
                }
            }
        });

        downloadlist.setOnClickListener(v -> {
            Intent intent = new Intent(Home.this, DownloadListActivity.class);
            startActivity(intent);
        });

        sllayout = findViewById(R.id.sllayout);
        ZoomControls zoomControls = findViewById(R.id.zoomControls);

        // Initialize ScaleGestureDetector for pinch-to-zoom
        scaleGestureDetector = new ScaleGestureDetector(this, new ScaleListener());

        // Set up zoom in and zoom out buttons
        zoomControls.setOnZoomInClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                scaleFactor += 0.1f; // Increase the scale factor
                sllayout.setScaleX(scaleFactor);
                sllayout.setScaleY(scaleFactor);
            }
        });

        zoomControls.setOnZoomOutClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                scaleFactor -= 0.1f; // Decrease the scale factor
                scaleFactor = Math.max(0.1f, scaleFactor); // Limit zoom out
                sllayout.setScaleX(scaleFactor);
                sllayout.setScaleY(scaleFactor);
            }
        });

        resetZoomBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Reset zoom
                scaleFactor = 1.0f;
                sllayout.setScaleX(scaleFactor);
                sllayout.setScaleY(scaleFactor);

                // Reset pan (translation)
                translationX = 0f;
                translationY = 0f;
                sllayout.setTranslationX(translationX);
                sllayout.setTranslationY(translationY);
            }
        });

        // Set a touch listener on the layout to detect pinch gestures and pan/slide
        sllayout.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                scaleGestureDetector.onTouchEvent(event);  // Detect the pinch-to-zoom gesture

                // If the user is not scaling, allow panning (moving the view)
                if (!isScaling) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            previousX = event.getX() - translationX;
                            previousY = event.getY() - translationY;
                            break;

                        case MotionEvent.ACTION_MOVE:
                            translationX = event.getX() - previousX;
                            translationY = event.getY() - previousY;

                            // Apply translation to the ScrollView
                            sllayout.setTranslationX(translationX);
                            sllayout.setTranslationY(translationY);
                            break;

                        case MotionEvent.ACTION_UP:
                            break;
                    }
                }
                return true;
            }
        });



        findViewById(R.id.uploadBtn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
                intent.setType("application/pdf");
                startActivityForResult(intent, REQUEST_PDF_SELECT);
            }
        });
        btnAddTable.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addTableLayout();
            }
        });

        btnRemoveTable.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removeTableLayout();
            }
        });

        add_vat_column.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addvatTableLayout();
            }
        });

        remove_vat_column.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removevatTableLayout();
            }
        });
        add_back_layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                banklay.setVisibility(View.VISIBLE);
            }
        });

        remove_back_layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                banklay.setVisibility(View.GONE);
            }
        });

        signatureLayout = findViewById(R.id.signaturelay);
        signatureImage = findViewById(R.id.signature_image);

        // Load saved signature image if exists
        loadSavedSignature();

        findViewById(R.id.add_signature).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                findViewById(R.id.signaturelay).setVisibility(View.VISIBLE);
                Intent intent = new Intent(Intent.ACTION_PICK);
                intent.setType("image/*");
                startActivityForResult(intent, PICK_SIGNATURE_IMAGE);
            }
        });

        findViewById(R.id.add_signature).setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                findViewById(R.id.signaturelay).setVisibility(View.GONE);
                return true;
            }
        });

        // Inflate the first table layout by default
        addTableLayout();


    }
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.text_to_number, menu); // Replace with your menu resource
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here
        int id = item.getItemId();

        if (id == R.id.action_open_dialog) {
            showNumberToWordsDialog();
            return true;
        } else if (id == R.id.invoice_traker) {
            openInvoiceTracker(); // Define this method to handle invoice tracking actions
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

// Add these new methods
private void saveSignatureUri(Uri uri) {
    SharedPreferences sharedPreferences = getSharedPreferences(SHARED_PREF_NAME, MODE_PRIVATE);
    SharedPreferences.Editor editor = sharedPreferences.edit();
    editor.putString(SIGNATURE_URI_KEY, uri.toString());
    editor.apply();
}

private void loadSavedSignature() {
    SharedPreferences sharedPreferences = getSharedPreferences(SHARED_PREF_NAME, MODE_PRIVATE);
    String uriString = sharedPreferences.getString(SIGNATURE_URI_KEY, null);

    if (uriString != null) {
        Uri uri = Uri.parse(uriString);
        ImageView signatureImage = findViewById(R.id.signature_image);
        findViewById(R.id.signaturelay).setVisibility(View.VISIBLE);
        signatureImage.setImageURI(uri);
    }
}







    private void openInvoiceTracker() {
        DataUploadDialogFragment dialogFragment = new DataUploadDialogFragment();
        dialogFragment.show(getSupportFragmentManager(), "DataUploadDialog");
    }

   /* private void openInvoiceTracker() {
        new MaterialAlertDialogBuilder(this)
                .setTitle("Invoice Tracker")
                .setMessage("Choose an option for the Invoice Tracker:")
                .setPositiveButton("Upload Data", (dialog, which) -> {
                    // Open the DataUploadDialogFragment
                    DataUploadDialogFragment dialogFragment = new DataUploadDialogFragment();
                    dialogFragment.show(getSupportFragmentManager(), "DataUploadDialog");
                })
                .setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss())
                .show();
    }*/

    private void showNumberToWordsDialog() {
        // Inflate the custom dialog layout
        LayoutInflater inflater = getLayoutInflater();
        View dialogView = inflater.inflate(R.layout.dialog_number_to_words, null);

        final EditText editTextNumber = dialogView.findViewById(R.id.editTextNumber);
        final TextView textViewWords = dialogView.findViewById(R.id.textViewWords);

        final Button buttonCopy = dialogView.findViewById(R.id.buttonCopy);

        editTextNumber.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String input = s.toString().trim();

                if (!input.isEmpty()) {
                    try {
                        String formattedText;
                        if (input.contains(".")) {
                            // Handle decimal numbers
                            String[] parts = input.split("\\.");
                            long integerPart = Long.parseLong(parts[0]); // Integer part
                            String integerWords = numToWord(integerPart);

                            String decimalWords = "";
                            if (parts.length > 1 && !parts[1].isEmpty()) {
                                decimalWords = convertDecimalToWords(parts[1]);
                            }

                            formattedText = "Total amount RO :- " + input + "/- " +
                                    "(Rial Omani " + integerWords + " rial and " +
                                    decimalWords + " baisa only)";

                        } else {
                            // Handle integer numbers only
                            long number = Long.parseLong(input);
                            String numberInWords = numToWord(number);

                            formattedText = "Total amount RO :- " + number + "/- " +
                                    "(Rial Omani " + numberInWords + " rial only)";
                        }

                        textViewWords.setText(formattedText);
                        edit_text.setText(formattedText);

                    } catch (NumberFormatException e) {
                        textViewWords.setText("Invalid number");
                    }
                } else {
                    textViewWords.setText("");
                }
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        buttonCopy.setOnClickListener(v -> copyToClipboard(textViewWords.getText().toString()));

        // Create the Material AlertDialog
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(this);
        builder.setView(dialogView)
                .setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss())
                .show();
    }

    private void copyToClipboard(String text) {
        ClipboardManager clipboard = (ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("Copied Text", text);
        clipboard.setPrimaryClip(clip);
    }

    private String numToWord(long inputNumber) {
        if (inputNumber == 0) {
            return "Zero";
        }

        String[] once = {"", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"};
        String[] twos = {"Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"};
        String[] tens = {"", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"};
        String[] thousands = {"", "Thousand", "Million", "Billion", "Trillion"};

        StringBuilder words = new StringBuilder();
        int thousandIndex = 0;

        while (inputNumber > 0) {
            int chunk = (int) (inputNumber % 1000);
            if (chunk != 0) {
                String chunkWords = convertChunkToWords(chunk);
                words.insert(0, chunkWords + " " + thousands[thousandIndex] + " ");
            }
            inputNumber /= 1000;
            thousandIndex++;
        }

        return words.toString().trim();
    }

    private String convertChunkToWords(int chunk) {
        String[] once = {"", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"};
        String[] twos = {"Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"};
        String[] tens = {"", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"};

        StringBuilder chunkWords = new StringBuilder();

        if (chunk >= 100) {
            chunkWords.append(once[chunk / 100]).append(" Hundred ");
            chunk %= 100;
        }

        if (chunk >= 20) {
            chunkWords.append(tens[chunk / 10]).append(" ");
            chunk %= 10;
        } else if (chunk >= 10) {
            chunkWords.append(twos[chunk - 10]).append(" ");
            chunk = 0;
        }

        if (chunk > 0) {
            chunkWords.append(once[chunk]).append(" ");
        }

        return chunkWords.toString().trim();
    }

    private String convertDecimalToWords(String decimalPart) {
        String[] once = {"Zero", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"};

        StringBuilder decimalWords = new StringBuilder();
        for (char digit : decimalPart.toCharArray()) {
            int num = Character.getNumericValue(digit);
            decimalWords.append(once[num]).append(" ");
        }

        return decimalWords.toString().trim();
    }


    // Helper method to convert a chunk (up to 3 digits) to words
//    private String convertChunkToWords(String chunk) {
//        String[] once = {"", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"};
//        String[] twos = {"Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"};
//        String[] tens = {"", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"};
//
//        StringBuilder chunkWords = new StringBuilder();
//        int chunkNumber = Integer.parseInt(chunk); // Safe to parse since the chunk is at most 3 digits
//
//        if (chunkNumber >= 100) {
//            chunkWords.append(once[chunkNumber / 100]).append(" Hundred ");
//            chunkNumber %= 100;
//        }
//
//        if (chunkNumber >= 20) {
//            chunkWords.append(tens[chunkNumber / 10]).append(" ");
//            chunkNumber %= 10;
//        } else if (chunkNumber >= 10) {
//            chunkWords.append(twos[chunkNumber - 10]).append(" ");
//            chunkNumber = 0; // Reset chunk since it's processed
//        }
//
//        if (chunkNumber > 0) {
//            chunkWords.append(once[chunkNumber]).append(" ");
//        }
//
//        return chunkWords.toString().trim();
//    }


    @Override
    public void onBackPressed() {
        // Inflate the custom layout
        LayoutInflater inflater = getLayoutInflater();
        View dialogView = inflater.inflate(R.layout.dialog_exit_confirmation, null);

        // Reference the checkbox, title, and buttons from the layout
        CheckBox checkboxConfirm = dialogView.findViewById(R.id.checkbox_confirm);
        TextView exitTitle = dialogView.findViewById(R.id.exit_title);
        Button buttonCancel = dialogView.findViewById(R.id.button_cancel);
        Button buttonYes = dialogView.findViewById(R.id.button_yes);
        exitTitle.setText("Are you sure you want to close First Invoice?");

        // Create the dialog
        AlertDialog dialog = new AlertDialog.Builder(this)
                .setView(dialogView)
                .create();

        // Set dialog background to transparent
        dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);

        // Initially disable the "Yes" button
        buttonYes.setEnabled(false);

        // Enable "Yes" button only when the checkbox is checked
        checkboxConfirm.setOnCheckedChangeListener((buttonView, isChecked) -> {
            buttonYes.setEnabled(isChecked);
        });

        // Set "Cancel" button to dismiss the dialog
        buttonCancel.setOnClickListener(v -> dialog.dismiss());

        // Set "Yes" button to exit the activity
        buttonYes.setOnClickListener(v -> {
            dialog.dismiss();
            super.onBackPressed();
        });

        dialog.show();
    }



    // Custom ScaleListener class for handling pinch-to-zoom
    private class ScaleListener extends ScaleGestureDetector.SimpleOnScaleGestureListener {
        @Override
        public boolean onScale(ScaleGestureDetector detector) {
            isScaling = true;  // Indicate that scaling is happening

            scaleFactor *= detector.getScaleFactor();
            scaleFactor = Math.max(0.1f, Math.min(scaleFactor, 5.0f)); // Limit scale factor

            // Apply the scaling to the ScrollView
            sllayout.setScaleX(scaleFactor);
            sllayout.setScaleY(scaleFactor);
            return true;
        }

        @Override
        public void onScaleEnd(ScaleGestureDetector detector) {
            super.onScaleEnd(detector);
            isScaling = false;  // Reset the scaling state after pinch is done
        }
    }
    private void showAlertDialog() {
        new MaterialAlertDialogBuilder(Home.this)
                .setTitle("Empty Subject!")
                .setMessage("Please your subject before downloading.")
                .setPositiveButton("OK", null)
                .show();
    }
    private void addTableLayout() {
        LayoutInflater inflater = LayoutInflater.from(this);
        View tableLayout = inflater.inflate(R.layout.item, null);
        tableContainer.addView(tableLayout, tableContainer.getChildCount()); // Add new table layout at the end
        isFirstTable = false; // Set flag to false after adding the first table layout
        // Set the serial number for the new table layout
        EditText serialEditText = tableLayout.findViewById(R.id.serial);
        serialEditText.setText(String.valueOf(serialNumber));
        serialNumber++; // Increment serial number for next table layout
    }

    private void removeTableLayout() {
        if (!isFirstTable && tableContainer.getChildCount() > 1) {
            View lastTableLayout = tableContainer.getChildAt(tableContainer.getChildCount() - 1);
            EditText serialEditText = lastTableLayout.findViewById(R.id.serial);
            int currentSerialNumber = Integer.parseInt(serialEditText.getText().toString());
            serialNumber = currentSerialNumber; // Update serial number
            tableContainer.removeViewAt(tableContainer.getChildCount() - 1);
        }
    }


    //private void add vatcolumn()
    private void addvatTableLayout() {
        LayoutInflater inflater = LayoutInflater.from(this);
        View tableLayout = inflater.inflate(R.layout.texinfo, null);
        vat_layout.addView(tableLayout, vat_layout.getChildCount()); // Add new table layout at the end
        isVatTable = false; // Set flag to false after adding the first table layout
    }
    private void removevatTableLayout() {
        if (!isVatTable && vat_layout.getChildCount() > 1) {
            View tableLayout = vat_layout.getChildAt(vat_layout.getChildCount() - 1);
            vat_layout.removeViewAt(vat_layout.getChildCount() - 1);
        }
    }






    //private void add vatcolumn



    private void layoutToPdfConverter() {
        if (checkPermission()) {
            LinearLayout layout = findViewById(R.id.sllayout);
            createPdf(layout);
        } else {
            requestPermission();
        }
    }

    private PdfDocument document;

    private void createPdf(LinearLayout layout) {
        document = new PdfDocument();
        PdfDocument.PageInfo pageInfo = new PdfDocument.PageInfo.Builder(layout.getWidth(), layout.getHeight(), 1).create();
        PdfDocument.Page page = document.startPage(pageInfo);

        Canvas canvas = page.getCanvas();
        canvas.drawColor(Color.WHITE);
        layout.draw(canvas);

        document.finishPage(page);

        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT_TREE);
        startActivityForResult(intent, 1);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == PICK_SIGNATURE_IMAGE && resultCode == RESULT_OK && data != null) {
            try {
                Uri imageUri = data.getData();
                if (imageUri != null) {
                    // Get the ImageView and Layout
                    ImageView signatureImage = findViewById(R.id.signature_image);
                    LinearLayout signatureLayout = findViewById(R.id.signaturelay);

                    // Make the signature layout visible
                    signatureLayout.setVisibility(View.VISIBLE);

                    // Load image using Glide (ensure Glide dependency is added)
                    Glide.with(this)
                        .load(imageUri)
                        .into(signatureImage);

                    // Save the signature image URI
                    saveSignatureUri(imageUri);

                    // Take persistable URI permission
                    final int takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION;
                    getContentResolver().takePersistableUriPermission(imageUri, takeFlags);
                }
            } catch (Exception e) {
                e.printStackTrace();
                Toast.makeText(this, "Failed to load image", Toast.LENGTH_SHORT).show();
            }
        } else if (requestCode == REQUEST_PDF_SELECT && resultCode == RESULT_OK) {
            Uri pdfUri = data.getData();
            uploadPdfToFirebase(pdfUri, "custom_file_name.pdf");
        } else if (requestCode == 1 && resultCode == RESULT_OK) {
            Uri treeUri = data.getData();
            DocumentFile pickedDir = DocumentFile.fromTreeUri(this, treeUri);

            // Create a custom layout for the file name input
            LayoutInflater inflater = getLayoutInflater();
            View dialogView = inflater.inflate(R.layout.custom_file_name_dialog, null);

            final AppCompatEditText fileNameInput = dialogView.findViewById(R.id.file_name_input);
            Button saveButton = dialogView.findViewById(R.id.save_button);
            Button cancelButton = dialogView.findViewById(R.id.cancel_button);

            // Set the title and buttons
            TextView titleView = dialogView.findViewById(R.id.title_text);
            titleView.setText("Enter file name");

            // Create an AlertDialog to get the file name from the user
            final AlertDialog alertDialog = new AlertDialog.Builder(this)
                    .setView(dialogView)
                    .create();
            alertDialog.show();

            saveButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    String fileName = fileNameInput.getText().toString();
                    if (!fileName.endsWith(".pdf")) {
                        fileName += ".pdf";
                    }
                    DocumentFile file = pickedDir.createFile("application/pdf", fileName);
                    try {
                        Log.d("PDF_CREATION", "Writing PDF to file...");
                        OutputStream out = getContentResolver().openOutputStream(file.getUri());
                        document.writeTo(out);
                        out.close();
                        document.close();
                        Log.d("PDF_CREATION", "PDF generated successfully!");

                        // Upload the PDF to Firebase Storage with the custom file name
                        uploadPdfToFirebase(file.getUri(), fileName);

                        Toast.makeText(Home.this, getString(R.string.pdf_generated_successfully), Toast.LENGTH_SHORT).show();
                        alertDialog.dismiss(); // Dismiss the dialog
                    } catch (IOException e) {
                        Log.e("PDF_CREATION", "Error generating PDF: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            });

            cancelButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    alertDialog.dismiss(); // Dismiss the dialog
                }
            });
        }
    }

    private void uploadPdfToFirebase(Uri pdfUri, String fileName) {
        final AlertDialog alertDialog = new AlertDialog.Builder(this)
                .setTitle("Uploading PDF...")
                .setMessage("Please wait...")
                .setView(R.layout.loading_dialog)
                .create();

        alertDialog.show();

        FirebaseStorage storage = FirebaseStorage.getInstance();
        StorageReference storageRef = storage.getReference("pdfs/" + fileName);

        UploadTask uploadTask = storageRef.putFile(pdfUri);

        uploadTask.addOnSuccessListener(new OnSuccessListener<UploadTask.TaskSnapshot>() {
                    @Override
                    public void onSuccess(UploadTask.TaskSnapshot taskSnapshot) {
                        alertDialog.dismiss();
                        // Get the download URL of the uploaded file
                        taskSnapshot.getStorage().getDownloadUrl().addOnSuccessListener(new OnSuccessListener<Uri>() {
                            @Override
                            public void onSuccess(Uri uri) {
                                Log.d("Upload", "File uploaded successfully: " + uri.toString());
                                Toast.makeText(Home.this, "PDF uploaded to Firebase Storage successfully!", Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                })
                .addOnFailureListener(new OnFailureListener() {
                    @Override
                    public void onFailure(@NonNull Exception e) {
                        alertDialog.dismiss();
                        Log.e("Upload", "Error uploading file: " + e.getMessage());
                        Toast.makeText(Home.this, "Error uploading PDF to Firebase Storage: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                })
                .addOnProgressListener(new OnProgressListener<UploadTask.TaskSnapshot>() {
                    @Override
                    public void onProgress(UploadTask.TaskSnapshot taskSnapshot) {
                        double progress = (100.0 * taskSnapshot.getBytesTransferred()) / taskSnapshot.getTotalByteCount();
                        alertDialog.setMessage("Uploading... " + String.format("%.2f", progress) + "%");
                    }
                });
    }

    private boolean checkPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return Environment.isExternalStorageManager();
        } else {
            int result = ContextCompat.checkSelfPermission(Home.this, Manifest.permission.READ_EXTERNAL_STORAGE);
            int result1 = ContextCompat.checkSelfPermission(Home.this, Manifest.permission.WRITE_EXTERNAL_STORAGE);
            return result == PackageManager.PERMISSION_GRANTED && result1 == PackageManager.PERMISSION_GRANTED;
        }
    }

    private void requestPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            try {
                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                intent.addCategory("android.intent.category.DEFAULT");
                intent.setData(Uri.parse(String.format("package:%s", getApplicationContext().getPackageName())));
                startActivityForResult(intent, REQUEST_PERMISSION_CODE_ANDROID_R);
            } catch (Exception e) {
                Intent intent = new Intent();
                intent.setAction(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
                startActivityForResult(intent, REQUEST_PERMISSION_CODE_ANDROID_R);
            }
        } else {
            //below android 11
            ActivityCompat.requestPermissions(Home.this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE}, REQUEST_PERMISSION_CODE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSION_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                layoutToPdfConverter();
            } else {
                Toast.makeText(this, "Permission denied", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void selectImageFromGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setType("image/*");
        startActivityForResult(intent, PICK_IMAGE_REQUEST);
    }

    private void hideSignature() {
        if (signatureLayout != null) {
            signatureLayout.setVisibility(View.GONE);
        }
    }
}