package com.official.invoicegenarator.expense;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.official.invoicegenarator.R;

import java.util.List;

public class IncomeFragment extends Fragment {

    private RecyclerView recyclerView;
    private IncomeAdapter incomeAdapter;
    private DatabaseHelper dbHelper;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_income, container, false);

        // Initialize RecyclerView and set its layout manager
        recyclerView = view.findViewById(R.id.recyclerViewIncome);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));

        // Initialize DatabaseHelper
        dbHelper = new DatabaseHelper(getContext());

        // Load the income transactions
        loadIncomeTransactions();

        return view;
    }

    private void loadIncomeTransactions() {
        // Retrieve the income transactions from the database
        List<Income> incomeList = dbHelper.getIncomeTransactions();

        // Set up the adapter with the income list and assign it to the RecyclerView
        incomeAdapter = new IncomeAdapter(incomeList);
        recyclerView.setAdapter(incomeAdapter);
    }

    @Override
    public void onResume() {
        super.onResume();
        // Refresh the data when the fragment is resumed
        loadIncomeTransactions();
    }
}
