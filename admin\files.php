<?php
/**
 * File Management
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require login
requireLogin();

$current_page = 'files';
$page_title = 'File Management';

// Handle file deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $filename = $_POST['filename'] ?? '';
    if ($filename) {
        $file_path = '../uploads/pdfs/' . basename($filename);
        if (file_exists($file_path)) {
            if (unlink($file_path)) {
                showAlert('File deleted successfully', 'success');
            } else {
                showAlert('Failed to delete file', 'danger');
            }
        } else {
            showAlert('File not found', 'warning');
        }
    }
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}

// Get file list
$upload_dir = '../uploads/pdfs/';
$files = [];

if (is_dir($upload_dir)) {
    $file_list = scandir($upload_dir);
    foreach ($file_list as $filename) {
        if ($filename === '.' || $filename === '..') {
            continue;
        }
        
        $file_path = $upload_dir . $filename;
        if (is_file($file_path)) {
            $files[] = [
                'name' => $filename,
                'size' => filesize($file_path),
                'type' => mime_content_type($file_path),
                'modified' => filemtime($file_path),
                'extension' => strtolower(pathinfo($filename, PATHINFO_EXTENSION))
            ];
        }
    }
    
    // Sort by modification date (newest first)
    usort($files, function($a, $b) {
        return $b['modified'] - $a['modified'];
    });
}

include 'includes/header.php';
?>

<!-- File Management Content -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">File Management</h1>
        <p class="text-muted">Manage uploaded PDF files and documents</p>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
            <i class="bi bi-cloud-upload me-2"></i>
            Upload File
        </button>
    </div>
</div>

<!-- File Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Files</h6>
                        <h3 class="mb-0"><?= count($files) ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-files" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Size</h6>
                        <h3 class="mb-0"><?= formatFileSize(array_sum(array_column($files, 'size'))) ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-hdd" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">PDF Files</h6>
                        <h3 class="mb-0"><?= count(array_filter($files, fn($f) => $f['extension'] === 'pdf')) ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-file-earmark-pdf" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Images</h6>
                        <h3 class="mb-0"><?= count(array_filter($files, fn($f) => in_array($f['extension'], ['jpg', 'jpeg', 'png']))) ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-image" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File List -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-folder me-2"></i>
            Uploaded Files
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($files)): ?>
            <div class="text-center py-5">
                <i class="bi bi-cloud-upload text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">No files uploaded</h4>
                <p class="text-muted">Upload your first file to get started.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="bi bi-cloud-upload me-2"></i>
                    Upload File
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>File</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Modified</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($files as $file): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-<?= getFileIcon($file['extension']) ?> me-2 text-primary"></i>
                                        <div>
                                            <div class="fw-medium"><?= htmlspecialchars($file['name']) ?></div>
                                            <small class="text-muted"><?= $file['type'] ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= strtoupper($file['extension']) ?></span>
                                </td>
                                <td><?= formatFileSize($file['size']) ?></td>
                                <td>
                                    <small><?= date('M j, Y g:i A', $file['modified']) ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="../uploads/pdfs/<?= urlencode($file['name']) ?>" 
                                           class="btn btn-outline-primary" target="_blank" title="View">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="../uploads/pdfs/<?= urlencode($file['name']) ?>" 
                                           class="btn btn-outline-success" download title="Download">
                                            <i class="bi bi-download"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteFile('<?= htmlspecialchars($file['name']) ?>')" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" action="<?= API_BASE_URL ?>files/upload" method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">Select File</label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required>
                        <div class="form-text">
                            Allowed types: PDF, JPG, PNG, DOC, DOCX. Maximum size: 50MB.
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="uploadFile()">
                    <i class="bi bi-cloud-upload me-2"></i>
                    Upload
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Form (hidden) -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="filename" id="deleteFilename">
</form>

<?php
function getFileIcon($extension) {
    return match($extension) {
        'pdf' => 'file-earmark-pdf',
        'jpg', 'jpeg', 'png' => 'image',
        'doc', 'docx' => 'file-earmark-word',
        default => 'file-earmark'
    };
}

$extra_js = "
<script>
function deleteFile(filename) {
    if (confirmDelete('Are you sure you want to delete this file? This action cannot be undone.')) {
        document.getElementById('deleteFilename').value = filename;
        document.getElementById('deleteForm').submit();
    }
}

function uploadFile() {
    const form = document.getElementById('uploadForm');
    const fileInput = document.getElementById('file');
    
    if (!fileInput.files[0]) {
        showToast('Please select a file', 'warning');
        return;
    }
    
    const formData = new FormData(form);
    const uploadBtn = event.target;
    const hideLoading = showLoading(uploadBtn);
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast('File uploaded successfully', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.error?.message || 'Upload failed', 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('Upload failed', 'danger');
        console.error('Error:', error);
    });
}
</script>
";

include 'includes/footer.php';
?>
