package com.official.invoicegenarator.network;

import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;

/**
 * HTTP Client for MtcInvoice API
 * Replaces Firebase functionality with custom backend calls
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class ApiClient {
    private static final String TAG = "ApiClient";
    private static final String BASE_URL = "http://*************/MtcInvoiceMasudvi/api/";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    private static ApiClient instance;
    private OkHttpClient httpClient;
    private Gson gson;
    private String authToken;
    
    private ApiClient() {
        initializeHttpClient();
        initializeGson();
    }
    
    public static synchronized ApiClient getInstance() {
        if (instance == null) {
            instance = new ApiClient();
        }
        return instance;
    }
    
    private void initializeHttpClient() {
        HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
        logging.setLevel(HttpLoggingInterceptor.Level.BODY);
        
        httpClient = new OkHttpClient.Builder()
                .addInterceptor(logging)
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }
    
    private void initializeGson() {
        gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .create();
    }
    
    /**
     * Set authentication token for API requests
     */
    public void setAuthToken(String token) {
        this.authToken = token;
    }
    
    /**
     * Clear authentication token
     */
    public void clearAuthToken() {
        this.authToken = null;
    }
    
    /**
     * Add authentication headers to request
     */
    private Request.Builder addAuthHeaders(Request.Builder builder) {
        if (authToken != null && !authToken.isEmpty()) {
            builder.addHeader("Authorization", "Bearer " + authToken);
        }
        return builder;
    }
    
    /**
     * Make GET request
     */
    public void get(String endpoint, ApiCallback<String> callback) {
        String url = BASE_URL + endpoint;
        
        Request request = addAuthHeaders(new Request.Builder())
                .url(url)
                .get()
                .build();
        
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "GET request failed: " + url, e);
                callback.onError("Network error: " + e.getMessage());
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                handleResponse(response, callback);
            }
        });
    }
    
    /**
     * Make POST request
     */
    public void post(String endpoint, Object data, ApiCallback<String> callback) {
        String url = BASE_URL + endpoint;
        String json = gson.toJson(data);
        
        RequestBody body = RequestBody.create(json, JSON);
        Request request = addAuthHeaders(new Request.Builder())
                .url(url)
                .post(body)
                .build();
        
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "POST request failed: " + url, e);
                callback.onError("Network error: " + e.getMessage());
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                handleResponse(response, callback);
            }
        });
    }
    
    /**
     * Make PUT request
     */
    public void put(String endpoint, Object data, ApiCallback<String> callback) {
        String url = BASE_URL + endpoint;
        String json = gson.toJson(data);
        
        RequestBody body = RequestBody.create(json, JSON);
        Request request = addAuthHeaders(new Request.Builder())
                .url(url)
                .put(body)
                .build();
        
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "PUT request failed: " + url, e);
                callback.onError("Network error: " + e.getMessage());
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                handleResponse(response, callback);
            }
        });
    }
    
    /**
     * Make DELETE request
     */
    public void delete(String endpoint, ApiCallback<String> callback) {
        String url = BASE_URL + endpoint;
        
        Request request = addAuthHeaders(new Request.Builder())
                .url(url)
                .delete()
                .build();
        
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "DELETE request failed: " + url, e);
                callback.onError("Network error: " + e.getMessage());
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                handleResponse(response, callback);
            }
        });
    }
    
    /**
     * Upload file with multipart form data
     */
    public void uploadFile(String endpoint, File file, ApiCallback<String> callback) {
        String url = BASE_URL + endpoint;
        
        RequestBody fileBody = RequestBody.create(file, MediaType.parse("application/octet-stream"));
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", file.getName(), fileBody)
                .build();
        
        Request request = addAuthHeaders(new Request.Builder())
                .url(url)
                .post(requestBody)
                .build();
        
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "File upload failed: " + url, e);
                callback.onError("Upload error: " + e.getMessage());
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                handleResponse(response, callback);
            }
        });
    }
    
    /**
     * Download file
     */
    public void downloadFile(String endpoint, ApiCallback<byte[]> callback) {
        String url = BASE_URL + endpoint;
        
        Request request = addAuthHeaders(new Request.Builder())
                .url(url)
                .get()
                .build();
        
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "File download failed: " + url, e);
                callback.onError("Download error: " + e.getMessage());
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful() && response.body() != null) {
                    byte[] bytes = response.body().bytes();
                    callback.onSuccess(bytes);
                } else {
                    callback.onError("Download failed: " + response.code() + " " + response.message());
                }
            }
        });
    }
    
    /**
     * Handle HTTP response
     */
    private void handleResponse(Response response, ApiCallback<String> callback) throws IOException {
        String responseBody = response.body() != null ? response.body().string() : "";
        
        if (response.isSuccessful()) {
            Log.d(TAG, "Request successful: " + response.code());
            callback.onSuccess(responseBody);
        } else {
            Log.e(TAG, "Request failed: " + response.code() + " " + responseBody);
            
            // Parse error message from response
            String errorMessage = parseErrorMessage(responseBody);
            callback.onError(errorMessage);
        }
    }
    
    /**
     * Parse error message from API response
     */
    private String parseErrorMessage(String responseBody) {
        try {
            ApiResponse errorResponse = gson.fromJson(responseBody, ApiResponse.class);
            if (errorResponse != null && errorResponse.getError() != null) {
                return errorResponse.getError().getMessage();
            }
        } catch (Exception e) {
            Log.w(TAG, "Failed to parse error response", e);
        }
        
        return "Request failed";
    }
    
    /**
     * Parse JSON response to object
     */
    public <T> T parseResponse(String json, Class<T> clazz) {
        try {
            return gson.fromJson(json, clazz);
        } catch (Exception e) {
            Log.e(TAG, "Failed to parse response", e);
            return null;
        }
    }
    
    /**
     * Convert object to JSON
     */
    public String toJson(Object object) {
        return gson.toJson(object);
    }
    
    /**
     * Check if network is available
     */
    public boolean isNetworkAvailable() {
        // This should be implemented with proper network checking
        // For now, return true
        return true;
    }
    
    /**
     * Cancel all pending requests
     */
    public void cancelAllRequests() {
        httpClient.dispatcher().cancelAll();
    }
}
