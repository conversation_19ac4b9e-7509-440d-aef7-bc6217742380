<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="20dp">


    <LinearLayout
        android:id="@+id/linearLayout3"
        android:layout_width="150dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingLeft="6dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@null"
                android:fontFamily="@font/timesnewromanbold"
                android:text="@string/to"
                android:textColor="@color/black"
                android:textSize="8sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@null"
                android:fontFamily="@font/timesnewromanbold"
                android:text="@string/sohar_international"
                android:textColor="@color/black"
                android:textSize="8sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            >

            <EditText
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@null"
                android:fontFamily="@font/timesnewromanbold"
                android:text="@string/po_box_sohar"
                android:textColor="@color/black"
                android:textSize="8sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <EditText
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@null"
                android:fontFamily="@font/timesnewromanbold"
                android:text="@string/address_sohar"
                android:textColor="@color/black"
                android:textSize="8sp"
                android:textStyle="bold" />

            <EditText
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@null"
                android:fontFamily="@font/timesnewromanbold"
                android:text="@string/vat_no"
                android:textColor="@color/black"
                android:textSize="8sp"
                android:textStyle="bold" />

            <EditText
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@null"
                android:fontFamily="@font/timesnewromanbold"
                android:text="VAT No: OM110000378X"
                android:textColor="@color/black"
                android:textSize="8sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linearLayout4"
        android:layout_width="150dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingLeft="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:id="@+id/date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@null"
                android:fontFamily="@font/timesnewromanbold"
                android:text="Date :........."
                android:textColor="@color/black"
                android:textSize="8sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:id="@+id/qrTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@null"
                android:fontFamily="@font/timesnewromanbold"
                android:text="QR No : QR 210724SIB"
                android:textColor="@color/black"
                android:textSize="8sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            >

            <EditText
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@null"
                android:fontFamily="@font/timesnewromanbold"
                android:text="Location : AKO Branch"
                android:textColor="@color/black"
                android:textSize="8sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            >

            <EditText
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@null"
                android:fontFamily="@font/timesnewromanbold"
                android:text="VAT IN : OM1100198193"
                android:textColor="@color/black"
                android:textSize="8sp"
                android:textStyle="bold" />

        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="@+id/linearLayout4"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="@+id/linearLayout3"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout3">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="18dp"
            android:gravity="left">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <EditText
                    android:id="@+id/subject"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/timesnewromanbold"
                    android:hint="Enter text here"
                    android:text="SUBJECT:   "
                    android:textColor="@color/black"
                    android:textSize="10sp"
                    android:textStyle="bold" />


            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/table_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- One table layout included by default -->
            <include layout="@layout/table" />

            <!-- More table layouts will be added here dynamically -->

        </LinearLayout>


    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>