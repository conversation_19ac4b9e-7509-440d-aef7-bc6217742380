package com.official.invoicegenarator.models;

import com.google.gson.annotations.SerializedName;

/**
 * Login response model
 */
public class LoginResponse {
    
    @SerializedName("token")
    private String token;
    
    @SerializedName("user")
    private User user;
    
    @SerializedName("expires_in")
    private long expiresIn;
    
    // Constructors
    public LoginResponse() {}
    
    public LoginResponse(String token, User user, long expiresIn) {
        this.token = token;
        this.user = user;
        this.expiresIn = expiresIn;
    }
    
    // Getters and Setters
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public long getExpiresIn() {
        return expiresIn;
    }
    
    public void setExpiresIn(long expiresIn) {
        this.expiresIn = expiresIn;
    }
    
    @Override
    public String toString() {
        return "LoginResponse{" +
                "token='" + (token != null ? "***" : "null") + '\'' +
                ", user=" + user +
                ", expiresIn=" + expiresIn +
                '}';
    }
}
