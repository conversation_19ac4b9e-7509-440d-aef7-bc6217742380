package com.official.invoicegenarator;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

public class InvoiceTraker extends AppCompatActivity {
    // Declare UI components
    private TabLayout tabLayout;
    private ViewPager2 viewPager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_invoice_traker); // Ensure this layout matches your XML

        // Initialize UI components
        tabLayout = findViewById(R.id.tabLayout); // Ensure this ID matches your XML
        viewPager = findViewById(R.id.viewPager); // Ensure this ID matches your XML

        // Set up the ViewPager with the adapter
        ViewPagerAdapter viewPagerAdapter = new ViewPagerAdapter(this);
        viewPager.setAdapter(viewPagerAdapter);

        // Link the TabLayout with ViewPager using TabLayoutMediator
        new TabLayoutMediator(tabLayout, viewPager, new TabLayoutMediator.TabConfigurationStrategy() {
            @Override
            public void onConfigureTab(@NonNull TabLayout.Tab tab, int position) {
                switch (position) {
                    case 0:
                        tab.setText("Iv Traker Data Upload"); // Tab title for the first fragment
                        break;
                    case 1:
                        tab.setText("Iv Traker Data Update/Delete"); // Tab title for the second fragment
                        break;
                }
            }
        }).attach();
    }
}
