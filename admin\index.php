<?php
/**
 * Admin Dashboard
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require login
requireLogin();

$current_page = 'dashboard';
$page_title = 'Dashboard';

// Get statistics from database
try {
    $pdo = getDbConnection();
    
    // Invoice statistics
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_invoices,
            COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_invoices,
            COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_invoices,
            COUNT(CASE WHEN payment_status = 'overdue' THEN 1 END) as overdue_invoices,
            SUM(CAST(amount AS DECIMAL(10,2))) as total_amount,
            AVG(CAST(amount AS DECIMAL(10,2))) as average_amount
        FROM invoice_data_items
    ");
    $invoice_stats = $stmt->fetch();
    
    // User statistics
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN status = 1 THEN 1 END) as active_users,
            COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users
        FROM users
    ");
    $user_stats = $stmt->fetch();
    
    // Recent invoices
    $stmt = $pdo->query("
        SELECT * FROM invoice_data_items 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $recent_invoices = $stmt->fetchAll();
    
    // Monthly invoice data for chart
    $stmt = $pdo->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as count,
            SUM(CAST(amount AS DECIMAL(10,2))) as total_amount
        FROM invoice_data_items 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    $monthly_data = $stmt->fetchAll();
    
    // Payment status data for pie chart
    $payment_data = [
        'paid' => $invoice_stats['paid_invoices'] ?? 0,
        'pending' => $invoice_stats['pending_invoices'] ?? 0,
        'overdue' => $invoice_stats['overdue_invoices'] ?? 0
    ];
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $invoice_stats = $user_stats = [];
    $recent_invoices = $monthly_data = [];
    $payment_data = ['paid' => 0, 'pending' => 0, 'overdue' => 0];
}

include 'includes/header.php';
?>

<!-- Dashboard Content -->
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">Dashboard</h1>
        <p class="text-muted">Welcome back, <?= getCurrentUser()['name'] ?>!</p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Invoices</h6>
                        <h2 class="mb-0"><?= number_format($invoice_stats['total_invoices'] ?? 0) ?></h2>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-receipt" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Paid Invoices</h6>
                        <h2 class="mb-0"><?= number_format($invoice_stats['paid_invoices'] ?? 0) ?></h2>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Amount</h6>
                        <h2 class="mb-0">$<?= number_format($invoice_stats['total_amount'] ?? 0, 2) ?></h2>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-currency-dollar" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Active Users</h6>
                        <h2 class="mb-0"><?= number_format($user_stats['active_users'] ?? 0) ?></h2>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart me-2"></i>
                    Monthly Invoice Trends
                </h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart me-2"></i>
                    Payment Status
                </h5>
            </div>
            <div class="card-body">
                <canvas id="paymentChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Invoices -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    Recent Invoices
                </h5>
                <a href="<?= ADMIN_URL ?>invoices.php" class="btn btn-primary btn-sm">
                    View All
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_invoices)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No invoices found</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Description</th>
                                    <th>Location</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_invoices as $invoice): ?>
                                    <tr>
                                        <td>
                                            <code><?= htmlspecialchars(substr($invoice['id'], 0, 8)) ?>...</code>
                                        </td>
                                        <td><?= htmlspecialchars($invoice['description']) ?></td>
                                        <td><?= htmlspecialchars($invoice['location']) ?></td>
                                        <td>$<?= number_format($invoice['amount'], 2) ?></td>
                                        <td>
                                            <?php
                                            $status_class = match($invoice['payment_status']) {
                                                'paid' => 'success',
                                                'pending' => 'warning',
                                                'overdue' => 'danger',
                                                default => 'secondary'
                                            };
                                            ?>
                                            <span class="badge bg-<?= $status_class ?>">
                                                <?= ucfirst($invoice['payment_status']) ?>
                                            </span>
                                        </td>
                                        <td><?= formatDate($invoice['created_at'], 'M j, Y') ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
$extra_js = "
<script>
// Monthly Chart
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
const monthlyChart = new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: " . json_encode(array_column($monthly_data, 'month')) . ",
        datasets: [{
            label: 'Invoice Count',
            data: " . json_encode(array_column($monthly_data, 'count')) . ",
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Payment Status Chart
const paymentCtx = document.getElementById('paymentChart').getContext('2d');
const paymentChart = new Chart(paymentCtx, {
    type: 'doughnut',
    data: {
        labels: ['Paid', 'Pending', 'Overdue'],
        datasets: [{
            data: [" . $payment_data['paid'] . ", " . $payment_data['pending'] . ", " . $payment_data['overdue'] . "],
            backgroundColor: [
                '#28a745',
                '#ffc107',
                '#dc3545'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
";

include 'includes/footer.php';
?>
