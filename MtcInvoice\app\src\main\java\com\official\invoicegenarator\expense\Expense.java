package com.official.invoicegenarator.expense;

public class Expense {
    private int id;            // Unique identifier for each expense
    private int amount;        // Amount of the expense
    private String category;   // Category of the expense (e.g., Food, Transport)
    private String date;       // Date of the expense
    private String note;       // Additional notes about the expense
    private String type;       // Type of transaction (income/expense)

    public Expense(int id, int amount, String category, String date, String note, String type) {
        this.id = id;
        this.amount = amount;
        this.category = category;
        this.date = date;
        this.note = note;
        this.type = type;  // Assign the type value
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getType() {
        return type; // Getter for type
    }

    public void setType(String type) {
        this.type = type; // Setter for type
    }

    // Override toString method for better debugging
    @Override
    public String toString() {
        return "Expense{" +
                "id=" + id +
                ", amount=" + amount +
                ", category='" + category + '\'' +
                ", date='" + date + '\'' +
                ", note='" + note + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}
