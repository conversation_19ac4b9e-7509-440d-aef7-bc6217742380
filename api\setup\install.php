<?php
/**
 * Database Setup and Installation Script
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';

// Only allow GET requests for safety
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendErrorResponse('Method not allowed', 405);
}

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE " . DB_NAME);
    
    // Read and execute schema file
    $schema_file = '../../database/schema.sql';
    
    if (!file_exists($schema_file)) {
        sendErrorResponse('Schema file not found', 500);
    }
    
    $schema_sql = file_get_contents($schema_file);
    
    // Split SQL statements
    $statements = array_filter(
        array_map('trim', explode(';', $schema_sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $executed_statements = 0;
    $errors = [];
    
    foreach ($statements as $statement) {
        try {
            if (trim($statement)) {
                $pdo->exec($statement);
                $executed_statements++;
            }
        } catch (PDOException $e) {
            // Skip errors for statements that might already exist
            if (strpos($e->getMessage(), 'already exists') === false) {
                $errors[] = $e->getMessage();
            }
        }
    }
    
    // Create uploads directory
    $upload_dir = '../uploads/pdfs/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Create default admin user if it doesn't exist
    try {
        $check_admin = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $admin_count = $check_admin->fetch()['count'];
        
        if ($admin_count == 0) {
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $pdo->exec("INSERT INTO users (name, email, password, role, status) VALUES 
                       ('Admin', '<EMAIL>', '$admin_password', 'admin', 1)");
        }
    } catch (PDOException $e) {
        $errors[] = "Failed to create admin user: " . $e->getMessage();
    }
    
    $response = [
        'database_created' => true,
        'statements_executed' => $executed_statements,
        'upload_directory_created' => is_dir($upload_dir),
        'admin_user_ready' => true,
        'default_credentials' => [
            'email' => '<EMAIL>',
            'password' => 'admin123'
        ]
    ];
    
    if (!empty($errors)) {
        $response['warnings'] = $errors;
    }
    
    sendSuccessResponse($response, 'Database setup completed successfully');
    
} catch (PDOException $e) {
    sendErrorResponse('Database setup failed: ' . $e->getMessage(), 500);
} catch (Exception $e) {
    sendErrorResponse('Setup failed: ' . $e->getMessage(), 500);
}
