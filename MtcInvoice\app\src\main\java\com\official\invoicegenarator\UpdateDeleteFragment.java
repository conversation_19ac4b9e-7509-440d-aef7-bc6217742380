package com.official.invoicegenarator;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.firebase.database.DataSnapshot;
import com.google.firebase.database.DatabaseError;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.firebase.database.ValueEventListener;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;

// Data model class for invoice items
class InvoiceDataItem {
    private String id; // Unique identifier for the item
    private String description; // Description of the invoice item
    private String location; // Location related to the invoice
    private String qr; // QR code data
    private String lpo; // Local Purchase Order number
    private String inb; // Invoice number or another identifier
    private String amount; // Amount of the invoice
    private String w_a; // Any other additional field
    private String paymentStatus; // Payment status
    private long timestamp; // Timestamp for when the item was created
    private String currentDateTime; // Field for formatted current date and time

    // Default constructor required for calls to DataSnapshot.getValue(InvoiceDataItem.class)
    public InvoiceDataItem() { }

    // Constructor to initialize all fields
    public InvoiceDataItem(String description, String location, String qr, String lpo, String inb, String amount, String w_a, String paymentStatus, long timestamp, String currentDateTime) {
        this.description = description;
        this.location = location;
        this.qr = qr;
        this.lpo = lpo;
        this.inb = inb;
        this.amount = amount;
        this.w_a = w_a;
        this.paymentStatus = paymentStatus;
        this.timestamp = timestamp;
        this.currentDateTime = currentDateTime; // Initialize the new field
    }

    // Getters and setters for all fields
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    public String getQr() { return qr; }
    public void setQr(String qr) { this.qr = qr; }
    public String getLpo() { return lpo; }
    public void setLpo(String lpo) { this.lpo = lpo; }
    public String getInb() { return inb; }
    public void setInb(String inb) { this.inb = inb; }
    public String getAmount() { return amount; }
    public void setAmount(String amount) { this.amount = amount; }
    public String getW_a() { return w_a; }
    public void setW_a(String w_a) { this.w_a = w_a; }
    public String getPaymentStatus() { return paymentStatus; }
    public void setPaymentStatus(String paymentStatus) { this.paymentStatus = paymentStatus; }
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    public String getCurrentDateTime() { return currentDateTime; }
    public void setCurrentDateTime(String currentDateTime) { this.currentDateTime = currentDateTime; }
}

// Main Fragment class for updating and deleting invoice data
public class UpdateDeleteFragment extends Fragment {

    private RecyclerView recyclerView;
    private DataAdapter dataAdapter;
    private ArrayList<InvoiceDataItem> dataList;
    private DatabaseReference databaseReference;
    private ProgressBar progressBar;
    private EditText searchBar;
    private LottieAnimationView lottieAnimationView;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_update_delete, container, false);

        databaseReference = FirebaseDatabase.getInstance().getReference("dataItems");

        recyclerView = view.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        lottieAnimationView = view.findViewById(R.id.lottieAnimationView);
        searchBar = view.findViewById(R.id.search_bar);
        dataList = new ArrayList<>();
        dataAdapter = new DataAdapter(dataList, new DataAdapter.OnItemClickListener() {
            @Override
            public void onEditClick(InvoiceDataItem item) {
                showUpdateDialog(item);
            }

            @Override
            public void onDeleteClick(String itemId) {
                showDeleteDialog(itemId);
            }
        });

        recyclerView.setAdapter(dataAdapter);
        loadData();

        searchBar.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                filterData(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
        return view;
    }

    private void loadData() {
        lottieAnimationView.setVisibility(View.VISIBLE); // Show Lottie animation
        databaseReference.addValueEventListener(new ValueEventListener() {
            @Override
            public void onDataChange(@NonNull DataSnapshot dataSnapshot) {
                dataList.clear(); // Clear the existing list
                List<InvoiceDataItem> tempList = new ArrayList<>(); // Temporary list

                for (DataSnapshot snapshot : dataSnapshot.getChildren()) {
                    InvoiceDataItem dataModel = snapshot.getValue(InvoiceDataItem.class);
                    if (dataModel != null) {
                        dataModel.setId(snapshot.getKey());

                        long timestamp = dataModel.getTimestamp();
                        SimpleDateFormat sdf = new SimpleDateFormat("hh:mm a, dd/MM/yyyy", Locale.getDefault()); // 12-hour format
                        String dateAndTime = sdf.format(new Date(timestamp));
                        dataModel.setCurrentDateTime(dateAndTime);

                        tempList.add(dataModel); // Add to temporary list
                    }
                }

                // Reverse the temporary list and add to the original dataList
                Collections.reverse(tempList);
                dataList.addAll(tempList);

                dataAdapter.notifyDataSetChanged(); // Notify the adapter
                lottieAnimationView.setVisibility(View.GONE); // Hide Lottie animation
            }

            @Override
            public void onCancelled(@NonNull DatabaseError databaseError) {
                Toast.makeText(getContext(), "Failed to load data: " + databaseError.getMessage(), Toast.LENGTH_SHORT).show();
                lottieAnimationView.setVisibility(View.GONE); // Hide Lottie animation
            }
        });
    }


    private void filterData(String query) {
        ArrayList<InvoiceDataItem> filteredList = new ArrayList<>();

        // Check if the query is empty
        if (query.isEmpty()) {
            // If empty, show all items
            filteredList.addAll(dataList);
        } else {
            // Otherwise, filter based on the query
            for (InvoiceDataItem item : dataList) {
                if (item.getDescription().toLowerCase().contains(query.toLowerCase()) ||
                        item.getLocation().toLowerCase().contains(query.toLowerCase()) ||
                        item.getQr().toLowerCase().contains(query.toLowerCase()) ||
                        item.getLpo().toLowerCase().contains(query.toLowerCase()) ||
                        item.getInb().toLowerCase().contains(query.toLowerCase()) ||
                        item.getCurrentDateTime().toLowerCase().contains(query.toLowerCase()) ||
                        item.getAmount().toLowerCase().contains(query.toLowerCase()) ||
                        item.getPaymentStatus().toLowerCase().contains(query.toLowerCase()) ||
                        item.getW_a().toLowerCase().contains(query.toLowerCase())) {
                    filteredList.add(item);
                }
            }
        }

        // Update the adapter with the filtered list
        dataAdapter.filterList(filteredList);
    }

    private void showUpdateDialog(InvoiceDataItem item) {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(getContext());
        builder.setTitle("Update Item");

        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_update_item, null);
        builder.setView(dialogView);

        EditText editTextDescription = dialogView.findViewById(R.id.editTextDescription);
        EditText editTextLocation = dialogView.findViewById(R.id.editTextLocation);
        EditText editTextQr = dialogView.findViewById(R.id.editTextQr);
        EditText editTextLpo = dialogView.findViewById(R.id.editTextLpo);
        EditText editTextInb = dialogView.findViewById(R.id.editTextInb);
        EditText editTextAmount = dialogView.findViewById(R.id.editTextAmount);
        EditText editTextW_a = dialogView.findViewById(R.id.editTextW_a);
        EditText editTextPaymentStatus = dialogView.findViewById(R.id.payment_status_update);

        // Set existing values in the dialog fields
        editTextDescription.setText(item.getDescription());
        editTextLocation.setText(item.getLocation());
        editTextQr.setText(item.getQr());
        editTextLpo.setText(item.getLpo());
        editTextInb.setText(item.getInb());
        editTextAmount.setText(item.getAmount());
        editTextW_a.setText(item.getW_a());
        editTextPaymentStatus.setText(item.getPaymentStatus());

        builder.setPositiveButton("Update", (dialog, which) -> {
            String updatedDescription = editTextDescription.getText().toString();
            String updatedLocation = editTextLocation.getText().toString();
            String updatedQr = editTextQr.getText().toString();
            String updatedLpo = editTextLpo.getText().toString();
            String updatedInb = editTextInb.getText().toString();
            String updatedAmount = editTextAmount.getText().toString();
            String updatedW_a = editTextW_a.getText().toString();
            String updatedPaymentStatus = editTextPaymentStatus.getText().toString();

            // Get the current timestamp
            long currentTimestamp = System.currentTimeMillis();
            SimpleDateFormat sdf = new SimpleDateFormat("hh:mm a, dd/MM/yyyy", Locale.getDefault()); // 12-hour format
            String currentDateTime = sdf.format(new Date(currentTimestamp));

            // Create an updated item with the new values and timestamp
            InvoiceDataItem updatedItem = new InvoiceDataItem(
                    updatedDescription, updatedLocation, updatedQr, updatedLpo, updatedInb,
                    updatedAmount, updatedW_a, updatedPaymentStatus, currentTimestamp, currentDateTime
            );
            updatedItem.setId(item.getId());

            databaseReference.child(item.getId()).setValue(updatedItem)
                    .addOnCompleteListener(task -> {
                        if (task.isSuccessful()) {
                            Toast.makeText(getContext(), "Item updated successfully", Toast.LENGTH_SHORT).show();
                        } else {
                            Toast.makeText(getContext(), "Failed to update item", Toast.LENGTH_SHORT).show();
                        }
                    });
        });

        builder.setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss());
        builder.show();
    }


    private void showDeleteDialog(String itemId) {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(getContext());
        builder.setTitle("Delete Item")
                .setMessage("Are you sure you want to delete this item?")
                .setPositiveButton("Delete", (dialog, which) -> deleteItem(itemId))
                .setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss())
                .show();
    }

    private void deleteItem(String itemId) {
        databaseReference.child(itemId).removeValue().addOnCompleteListener(task -> {
            if (task.isSuccessful()) {
                Toast.makeText(getContext(), "Item deleted successfully", Toast.LENGTH_SHORT).show();
                loadData(); // Reload data to reflect deletion
            } else {
                Toast.makeText(getContext(), "Failed to delete item", Toast.LENGTH_SHORT).show();
            }
        });
    }


}

// Adapter class to bind invoice data to the RecyclerView
class DataAdapter extends RecyclerView.Adapter<DataAdapter.ViewHolder> {
    private ArrayList<InvoiceDataItem> dataList;
    private OnItemClickListener listener;

    public interface OnItemClickListener {
        void onEditClick(InvoiceDataItem item);
        void onDeleteClick(String itemId);
    }

    public DataAdapter(ArrayList<InvoiceDataItem> dataList, OnItemClickListener listener) {
        this.dataList = dataList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_data_invoice_traker, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        InvoiceDataItem item = dataList.get(position);

        holder.textViewDescription.setText("Description: " + item.getDescription());
        holder.textViewLocation.setText("Location: " + item.getLocation());
        holder.textViewQr.setText("QR Code: " + item.getQr());
        holder.textViewLpo.setText("LPO: " + item.getLpo());
        holder.textViewInb.setText("Invoice Number: " + item.getInb());
        holder.textViewAmount.setText("Amount: " + item.getAmount());
        holder.textViewW_a.setText("W/A: " + item.getW_a());
        holder.textViewPaymentStatus.setText("Payment Status: " + item.getPaymentStatus());

        holder.textTimeDate.setText("Time and Date:\n" + item.getCurrentDateTime());

        holder.buttonEdit.setOnClickListener(v -> listener.onEditClick(item));
        holder.buttonDelete.setOnClickListener(v -> listener.onDeleteClick(item.getId()));
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public void filterList(ArrayList<InvoiceDataItem> filteredList) {
        this.dataList = filteredList;
        notifyDataSetChanged();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textViewDescription, textViewLocation, textViewQr, textViewLpo, textViewInb, textViewAmount, textViewW_a, textViewPaymentStatus, textTimeDate;
        Button buttonEdit, buttonDelete;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            textViewDescription = itemView.findViewById(R.id.text_description);
            textViewLocation = itemView.findViewById(R.id.text_location);
            textViewQr = itemView.findViewById(R.id.text_qr);
            textViewLpo = itemView.findViewById(R.id.text_lpo);
            textViewInb = itemView.findViewById(R.id.text_inb);
            textViewAmount = itemView.findViewById(R.id.text_amount);
            textViewW_a = itemView.findViewById(R.id.text_w_a);
            textViewPaymentStatus = itemView.findViewById(R.id.text_payment_status);
            textTimeDate = itemView.findViewById(R.id.text_time_date); // TextView for date and time
            buttonEdit = itemView.findViewById(R.id.button_edit);
            buttonDelete = itemView.findViewById(R.id.button_delete);
        }
    }
}
