<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#2f3640"
    android:orientation="vertical"
    android:padding="16dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded"
        android:layout_marginBottom="5dp"
        android:orientation="vertical"
        >
        <RadioGroup
            android:id="@+id/sortOptions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/sortNewest"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="নতৃুন আপডেট"
                android:checked="true"/>

            <RadioButton
                android:id="@+id/sortOldest"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="পুরাতন আপডেট"/>
        </RadioGroup>

    </LinearLayout>

    <EditText
        android:id="@+id/searchEditText"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:background="@drawable/rounded_search_background"
        android:drawableStart="@drawable/ic_search"
        android:drawablePadding="8dp"
        android:hint="Search PDFs"
        android:inputType="text"
        android:padding="8dp"
        />

    <ProgressBar
        android:id="@+id/loadingProgressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="12dp"
        android:visibility="visible"
        android:scrollbars="vertical"
        android:overScrollMode="ifContentScrolls"
        android:scrollbarStyle="insideOverlay"
        android:paddingEnd="4dp"
        android:paddingBottom="4dp" />

</LinearLayout>
