package com.official.invoicegenarator.expense;

import android.app.DatePickerDialog;
import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;

import com.official.invoicegenarator.R;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public class AddExpenseDialogFragment extends DialogFragment {

    private EditText etAmount, etCategory, etDate, etNote;
    private Button btnPickDate;

    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        LayoutInflater inflater = requireActivity().getLayoutInflater();
        View view = inflater.inflate(R.layout.dialog_add_expense, null);

        etAmount = view.findViewById(R.id.etAmount);
        etCategory = view.findViewById(R.id.etCategory);
        etDate = view.findViewById(R.id.etDate);
        etNote = view.findViewById(R.id.etNote);
        btnPickDate = view.findViewById(R.id.btnPickDate);

        // Set up the date picker
        btnPickDate.setOnClickListener(v -> showDatePicker());

        AlertDialog.Builder builder = new AlertDialog.Builder(requireActivity());
        builder.setView(view)
                .setTitle("Add Expense")
                .setPositiveButton("Save", (dialog, which) -> saveExpense())
                .setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss());

        return builder.create();
    }

    private void showDatePicker() {
        Calendar calendar = Calendar.getInstance();
        DatePickerDialog datePickerDialog = new DatePickerDialog(
                requireActivity(),
                (view, year, month, dayOfMonth) -> {
                    Calendar selectedDate = Calendar.getInstance();
                    selectedDate.set(year, month, dayOfMonth);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
                    etDate.setText(sdf.format(selectedDate.getTime()));
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );
        datePickerDialog.show();
    }

    private void saveExpense() {
        String amountStr = etAmount.getText().toString();
        String category = etCategory.getText().toString();
        String date = etDate.getText().toString();
        String note = etNote.getText().toString();

        if (!amountStr.isEmpty() && !category.isEmpty() && !date.isEmpty()) {
            int amount = Integer.parseInt(amountStr);
            DatabaseHelper dbHelper = new DatabaseHelper(requireActivity());
            dbHelper.addTransaction(amount, category, date, note, "expense");
        }
    }
}
