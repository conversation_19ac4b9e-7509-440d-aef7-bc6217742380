package com.official.invoicegenarator.expense;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import java.util.ArrayList;
import java.util.List;

public class DatabaseHelper extends SQLiteOpenHelper {

    private static final String DATABASE_NAME = "ExpenseManager.db";
    private static final int DATABASE_VERSION = 1;

    private static final String TABLE_TRANSACTIONS = "transactions";
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_AMOUNT = "amount";
    private static final String COLUMN_CATEGORY = "category";
    private static final String COLUMN_DATE = "date";
    private static final String COLUMN_NOTE = "note";
    private static final String COLUMN_TYPE = "type"; // Column to differentiate between income and expense

    private static final String TYPE_INCOME = "income";
    private static final String TYPE_EXPENSE = "expense";

    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        String createTableTransactions = "CREATE TABLE " + TABLE_TRANSACTIONS + " ("
                + COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, "
                + COLUMN_AMOUNT + " INTEGER, "
                + COLUMN_CATEGORY + " TEXT, "
                + COLUMN_DATE + " TEXT, "
                + COLUMN_NOTE + " TEXT, "
                + COLUMN_TYPE + " TEXT)";
        db.execSQL(createTableTransactions);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_TRANSACTIONS);
        onCreate(db);
    }

    // Method to add a transaction
    public void addTransaction(int amount, String category, String date, String note, String type) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        values.put(COLUMN_AMOUNT, amount);
        values.put(COLUMN_CATEGORY, category);
        values.put(COLUMN_DATE, date);
        values.put(COLUMN_NOTE, note);
        values.put(COLUMN_TYPE, type);

        db.insert(TABLE_TRANSACTIONS, null, values);
        db.close();
    }
    // Method to retrieve transactions by type
    public Cursor getTransactionsByType(String type) {
        SQLiteDatabase db = this.getReadableDatabase();
        return db.query(TABLE_TRANSACTIONS,
                null,
                COLUMN_TYPE + " = ?",
                new String[]{type},
                null,
                null,
                COLUMN_DATE + " DESC");
    }

    // Method to retrieve income transactions
    public List<Income> getIncomeTransactions() {
        List<Income> incomeList = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.query(TABLE_TRANSACTIONS,
                null,
                COLUMN_TYPE + " = ?",
                new String[]{TYPE_INCOME},
                null,
                null,
                COLUMN_DATE + " DESC");

        if (cursor.moveToFirst()) {
            do {
                int id = cursor.getInt(cursor.getColumnIndex(COLUMN_ID));
                int amount = cursor.getInt(cursor.getColumnIndex(COLUMN_AMOUNT));
                String category = cursor.getString(cursor.getColumnIndex(COLUMN_CATEGORY));
                String date = cursor.getString(cursor.getColumnIndex(COLUMN_DATE));
                String note = cursor.getString(cursor.getColumnIndex(COLUMN_NOTE));
                String type = cursor.getString(cursor.getColumnIndex(COLUMN_TYPE));

                Income income = new Income(id, amount, category, date, note, type);
                incomeList.add(income);
            } while (cursor.moveToNext());
        }

        cursor.close();
        db.close();
        return incomeList;
    }

    // Method to retrieve expense transactions
    public List<Expense> getExpenseTransactions() {
        List<Expense> expenseList = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.query(TABLE_TRANSACTIONS,
                null,
                COLUMN_TYPE + " = ?",
                new String[]{TYPE_EXPENSE},
                null,
                null,
                COLUMN_DATE + " DESC");

        if (cursor.moveToFirst()) {
            do {
                int id = cursor.getInt(cursor.getColumnIndex(COLUMN_ID));
                int amount = cursor.getInt(cursor.getColumnIndex(COLUMN_AMOUNT));
                String category = cursor.getString(cursor.getColumnIndex(COLUMN_CATEGORY));
                String date = cursor.getString(cursor.getColumnIndex(COLUMN_DATE));
                String note = cursor.getString(cursor.getColumnIndex(COLUMN_NOTE));
                String type = cursor.getString(cursor.getColumnIndex(COLUMN_TYPE));

                Expense expense = new Expense(id, amount, category, date, note, type);
                expenseList.add(expense);
            } while (cursor.moveToNext());
        }

        cursor.close();
        db.close();
        return expenseList;
    }
}
