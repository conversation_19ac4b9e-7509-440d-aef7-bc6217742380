<?php
/**
 * Invoice Form (Add/Edit)
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require login
requireLogin();

$current_page = 'invoices';
$edit_mode = isset($_GET['id']);
$invoice_id = $edit_mode ? $_GET['id'] : null;
$page_title = $edit_mode ? 'Edit Invoice' : 'Add New Invoice';

// Initialize form data
$form_data = [
    'description' => '',
    'location' => '',
    'qr' => '',
    'lpo' => '',
    'inb' => '',
    'amount' => '',
    'w_a' => '',
    'payment_status' => 'pending'
];

// Load existing data if editing
if ($edit_mode && $invoice_id) {
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->prepare("SELECT * FROM invoice_data_items WHERE id = ?");
        $stmt->execute([$invoice_id]);
        $existing_data = $stmt->fetch();
        
        if ($existing_data) {
            $form_data = array_merge($form_data, $existing_data);
        } else {
            showAlert('Invoice not found', 'danger');
            header('Location: ' . ADMIN_URL . 'invoices.php');
            exit();
        }
    } catch (Exception $e) {
        showAlert('Failed to load invoice data', 'danger');
        error_log("Load invoice error: " . $e->getMessage());
        header('Location: ' . ADMIN_URL . 'invoices.php');
        exit();
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo = getDbConnection();
        
        // Validate and sanitize input
        $data = [
            'description' => sanitize($_POST['description'] ?? ''),
            'location' => sanitize($_POST['location'] ?? ''),
            'qr' => sanitize($_POST['qr'] ?? ''),
            'lpo' => sanitize($_POST['lpo'] ?? ''),
            'inb' => sanitize($_POST['inb'] ?? ''),
            'amount' => sanitize($_POST['amount'] ?? ''),
            'w_a' => sanitize($_POST['w_a'] ?? ''),
            'payment_status' => sanitize($_POST['payment_status'] ?? 'pending')
        ];
        
        // Validate required fields
        $required_fields = ['description', 'location', 'qr', 'lpo', 'inb', 'amount', 'w_a'];
        $missing_fields = [];
        
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                $missing_fields[] = ucfirst(str_replace('_', ' ', $field));
            }
        }
        
        if (!empty($missing_fields)) {
            throw new Exception('Missing required fields: ' . implode(', ', $missing_fields));
        }
        
        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] < 0) {
            throw new Exception('Amount must be a valid positive number');
        }
        
        if ($edit_mode) {
            // Update existing invoice
            $data['timestamp'] = time() * 1000; // Firebase-style timestamp
            $data['current_date_time'] = date('h:i A, d/m/Y', $data['timestamp'] / 1000);
            
            $stmt = $pdo->prepare("
                UPDATE invoice_data_items 
                SET description = ?, location = ?, qr = ?, lpo = ?, inb = ?, 
                    amount = ?, w_a = ?, payment_status = ?, timestamp = ?, current_date_time = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                $data['description'], $data['location'], $data['qr'], $data['lpo'], 
                $data['inb'], $data['amount'], $data['w_a'], $data['payment_status'],
                $data['timestamp'], $data['current_date_time'], $invoice_id
            ]);
            
            showAlert('Invoice updated successfully', 'success');
        } else {
            // Create new invoice
            $data['id'] = generatePushKey();
            $data['timestamp'] = time() * 1000;
            $data['current_date_time'] = date('h:i A, d/m/Y', $data['timestamp'] / 1000);
            
            $stmt = $pdo->prepare("
                INSERT INTO invoice_data_items 
                (id, description, location, qr, lpo, inb, amount, w_a, payment_status, timestamp, current_date_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['id'], $data['description'], $data['location'], $data['qr'], 
                $data['lpo'], $data['inb'], $data['amount'], $data['w_a'], 
                $data['payment_status'], $data['timestamp'], $data['current_date_time']
            ]);
            
            showAlert('Invoice created successfully', 'success');
        }
        
        header('Location: ' . ADMIN_URL . 'invoices.php');
        exit();
        
    } catch (Exception $e) {
        showAlert($e->getMessage(), 'danger');
        $form_data = array_merge($form_data, $_POST);
    }
}

/**
 * Generate Firebase-style push key
 */
function generatePushKey() {
    $chars = '-0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_abcdefghijklmnopqrstuvwxyz';
    $key = '';
    for ($i = 0; $i < 20; $i++) {
        $key .= $chars[mt_rand(0, strlen($chars) - 1)];
    }
    return $key;
}

include 'includes/header.php';
?>

<!-- Invoice Form Content -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0"><?= $page_title ?></h1>
        <p class="text-muted"><?= $edit_mode ? 'Update invoice information' : 'Create a new invoice data item' ?></p>
    </div>
    <div class="col-md-6 text-end">
        <a href="<?= ADMIN_URL ?>invoices.php" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Invoices
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-receipt me-2"></i>
                    Invoice Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="Enter invoice description" required><?= htmlspecialchars($form_data['description']) ?></textarea>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   value="<?= htmlspecialchars($form_data['location']) ?>" 
                                   placeholder="Enter location" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="qr" class="form-label">QR Code <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="qr" name="qr" 
                                   value="<?= htmlspecialchars($form_data['qr']) ?>" 
                                   placeholder="Enter QR code" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="lpo" class="form-label">LPO <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="lpo" name="lpo" 
                                   value="<?= htmlspecialchars($form_data['lpo']) ?>" 
                                   placeholder="Enter LPO number" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="inb" class="form-label">INB <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="inb" name="inb" 
                                   value="<?= htmlspecialchars($form_data['inb']) ?>" 
                                   placeholder="Enter INB number" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       value="<?= htmlspecialchars($form_data['amount']) ?>" 
                                       placeholder="0.00" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="w_a" class="form-label">W/A <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="w_a" name="w_a" 
                                   value="<?= htmlspecialchars($form_data['w_a']) ?>" 
                                   placeholder="Enter W/A information" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="payment_status" class="form-label">Payment Status <span class="text-danger">*</span></label>
                            <select class="form-select" id="payment_status" name="payment_status" required>
                                <option value="pending" <?= $form_data['payment_status'] === 'pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="paid" <?= $form_data['payment_status'] === 'paid' ? 'selected' : '' ?>>Paid</option>
                                <option value="overdue" <?= $form_data['payment_status'] === 'overdue' ? 'selected' : '' ?>>Overdue</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?= ADMIN_URL ?>invoices.php" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            <?= $edit_mode ? 'Update Invoice' : 'Create Invoice' ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Information
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>Required Fields</h6>
                    <p class="text-muted small">All fields marked with <span class="text-danger">*</span> are required.</p>
                </div>
                
                <div class="mb-3">
                    <h6>Payment Status</h6>
                    <ul class="list-unstyled small text-muted">
                        <li><span class="badge bg-warning me-2">Pending</span>Payment not yet received</li>
                        <li><span class="badge bg-success me-2">Paid</span>Payment completed</li>
                        <li><span class="badge bg-danger me-2">Overdue</span>Payment is overdue</li>
                    </ul>
                </div>
                
                <?php if ($edit_mode): ?>
                    <div class="mb-3">
                        <h6>Invoice ID</h6>
                        <code class="small"><?= htmlspecialchars($invoice_id) ?></code>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Created</h6>
                        <small class="text-muted"><?= formatDate($form_data['created_at'] ?? '', 'M j, Y g:i A') ?></small>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Last Updated</h6>
                        <small class="text-muted"><?= formatDate($form_data['updated_at'] ?? '', 'M j, Y g:i A') ?></small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
