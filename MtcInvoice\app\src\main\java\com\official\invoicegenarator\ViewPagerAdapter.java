package com.official.invoicegenarator;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

public class ViewPagerAdapter extends FragmentStateAdapter {

    // Constructor
    public ViewPagerAdapter(FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return new DataUploadFragment(); // Replace with your actual fragment for data upload
            case 1:
                return new UpdateDeleteFragment(); // Replace with your actual fragment for update/delete
            default:
                return new DataUploadFragment(); // Default case
        }
    }

    @Override
    public int getItemCount() {
        return 2; // Number of tabs
    }
}
