<?php
/**
 * File List API
 * List uploaded files (replaces Firebase Storage list functionality)
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';
require_once '../utils/jwt.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendErrorResponse('Method not allowed', 405);
}

try {
    // Authentication required
    $user = JWT::requireAuth(JWT_SECRET_KEY);
    
    // Get upload directory
    $upload_dir = UPLOAD_DIR . 'pdfs/';
    
    if (!is_dir($upload_dir)) {
        sendSuccessResponse(['files' => []], 'No files found');
    }
    
    // Get all files in directory
    $files = [];
    $file_list = scandir($upload_dir);
    
    foreach ($file_list as $filename) {
        if ($filename === '.' || $filename === '..') {
            continue;
        }
        
        $file_path = $upload_dir . $filename;
        
        if (is_file($file_path)) {
            $file_info = [
                'name' => $filename,
                'original_name' => $filename, // Could be enhanced to store original names
                'size' => filesize($file_path),
                'type' => mime_content_type($file_path),
                'extension' => strtolower(pathinfo($filename, PATHINFO_EXTENSION)),
                'modified' => date('Y-m-d H:i:s', filemtime($file_path)),
                'download_url' => BASE_URL . 'files/download.php?file=' . urlencode($filename)
            ];
            
            $files[] = $file_info;
        }
    }
    
    // Sort files by modification date (newest first)
    usort($files, function($a, $b) {
        return strtotime($b['modified']) - strtotime($a['modified']);
    });
    
    // Apply pagination
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    
    $total_files = count($files);
    $paginated_files = array_slice($files, $offset, $limit);
    
    sendSuccessResponse([
        'files' => $paginated_files,
        'pagination' => [
            'total' => $total_files,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + count($paginated_files)) < $total_files
        ]
    ], 'Files retrieved successfully');
    
} catch (Exception $e) {
    error_log("File list error: " . $e->getMessage());
    sendErrorResponse('Failed to retrieve file list', 500);
}
